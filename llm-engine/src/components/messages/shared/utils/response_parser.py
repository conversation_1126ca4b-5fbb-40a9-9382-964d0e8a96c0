"""
Common response parsing utilities for the messages component.

This module provides utilities for parsing LLM responses, extracting JSON,
and handling different response formats consistently across processes.
"""

import json
from typing import Dict, Any, Union, Optional
from src.infrastructure.log.unified_logger import debug, warning, error


def extract_json_from_response(llm_response: Union[Dict[str, Any], str]) -> Optional[Dict[str, Any]]:
    """
    Extract JSON data from various LLM response formats.

    Args:
        llm_response: The LLM response in various formats

    Returns:
        Extracted JSON data as a dictionary, or None if extraction fails
    """
    try:
        # If it's already a dict, return it
        if isinstance(llm_response, dict):
            # Check if it's a wrapped response
            if "response" in llm_response:
                response_text = llm_response["response"]
                return _extract_json_from_text(response_text)
            else:
                return llm_response

        # If it's a string, try to extract JSON
        elif isinstance(llm_response, str):
            return _extract_json_from_text(llm_response)

        else:
            warning(f"Unexpected response type: {type(llm_response)}")
            return None

    except Exception as e:
        error(f"Error extracting JSO<PERSON> from response: {e}")
        return None


def _extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON from text, handling markdown code blocks and direct JSON.

    Args:
        text: The text to extract JSON from

    Returns:
        Extracted JSON data as a dictionary, or None if extraction fails
    """
    try:
        # Handle markdown JSON code blocks
        if "```json" in text and "```" in text:
            start = text.find("```json") + 7
            end = text.rfind("```")
            json_text = text[start:end].strip()
            debug(f"Extracted JSON from markdown: {json_text}")
            return json.loads(json_text)

        # Handle direct JSON response
        elif text.strip().startswith("{") and text.strip().endswith("}"):
            debug(f"Parsing direct JSON: {text}")
            return json.loads(text.strip())

        # Try to find JSON within the text
        else:
            # Look for JSON-like patterns
            start_idx = text.find("{")
            if start_idx != -1:
                # Find the matching closing brace
                brace_count = 0
                end_idx = start_idx
                for i, char in enumerate(text[start_idx:], start_idx):
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i
                            break

                if brace_count == 0:
                    json_text = text[start_idx:end_idx + 1]
                    debug(f"Extracted JSON from text: {json_text}")
                    return json.loads(json_text)

            warning(f"No JSON found in text: {text}")
            return None

    except json.JSONDecodeError as e:
        error(f"JSON decode error: {e}")
        return None
    except Exception as e:
        error(f"Unexpected error extracting JSON: {e}")
        return None


def parse_classification_response(llm_response: Union[Dict[str, Any], str]) -> Dict[str, Any]:
    """
    Parse classification response and extract subcategories, location_name, and search_method.

    Args:
        llm_response: The LLM response from classification

    Returns:
        Dictionary with parsed classification data
    """
    extracted_json = extract_json_from_response(llm_response)

    if not extracted_json:
        return {
            "subcategories": [],
            "location_name": None,
            "search_method": "user_location",
            "error": "Failed to extract JSON from classification response"
        }

    # Extract and validate required fields
    subcategories = extracted_json.get("subcategories", [])
    location_name = extracted_json.get("location_name")
    search_method = extracted_json.get("search_method", "user_location")
    tags = extracted_json.get("tags", [])
    categories = extracted_json.get("categories", [])

    # Ensure subcategories is a list
    if not isinstance(subcategories, list):
        subcategories = []

    # Ensure tags is a list
    if not isinstance(tags, list):
        tags = []

    # Ensure categories is a list
    if not isinstance(categories, list):
        categories = []

    # Validate search_method
    valid_search_methods = ["user_location",
                            "location_search", "boundary", "point_radius"]
    if search_method not in valid_search_methods:
        search_method = "user_location"

    return {
        "subcategories": subcategories,
        "location_name": location_name,
        "search_method": search_method,
        "tags": tags,
        "categories": categories
    }


def parse_available_categories(categories_str: str) -> list:
    """
    Parse available categories string into a list of subcategories.

    Args:
        categories_str: String containing available categories

    Returns:
        List of available subcategories
    """
    available_subcategories = []

    for line in categories_str.splitlines():
        if ':' in line:
            _, subcats = line.split(':', 1)
            available_subcategories.extend(
                [s.strip() for s in subcats.split(',') if s.strip()]
            )

    return available_subcategories


def format_response_with_session(
    status_code: int,
    response_text: str,
    top_candidates: list,
    session_title: Optional[str] = None,
    error_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Format a standard response dictionary with optional session information.

    Args:
        status_code: The status code for the response
        response_text: The response text
        top_candidates: List of top candidates
        session_title: Optional session title
        error_context: Optional error context for debugging

    Returns:
        Formatted response dictionary
    """
    response = {
        "status_code": status_code,
        "response": response_text,
        "top_candidates": top_candidates
    }

    if session_title:
        response["session_title"] = session_title

    if error_context:
        response["error_context"] = error_context

    return response
