/** @format */

'use client';

import { colors } from '@/app/colors';
import { useLocationManager } from '@/app/shared/locationManager';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { Suspense, useEffect, useState } from 'react';
import { FaMapMarkerAlt, FaSave, FaTrash } from 'react-icons/fa';
import { FiBell, FiEye, FiMapPin, FiSettings, FiZap } from 'react-icons/fi';

interface QuickSetting {
	id: string;
	label: string;
	description: string;
	icon: React.ReactNode;
	enabled: boolean;
	category: 'location' | 'notifications' | 'privacy' | 'interface';
}

function SettingsPageContent() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const activeTab = searchParams.get('tab') || 'general';
	const returnUrl = searchParams.get('return') || '/settings';

	const {
		location: userLocation,
		setManualLocation,
		clearLocation,
		toggleLiveLocation,
	} = useLocationManager();
	const { data: session } = useSession();

	// Redirect to sign in if not authenticated
	if (!session) {
		router.push('/auth/signin');
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4'></div>
					<p style={{ color: colors.neutral.slateGray }}>
						Redirecting to sign in...
					</p>
				</div>
			</div>
		);
	}

	const [manualLat, setManualLat] = useState('');
	const [manualLng, setManualLng] = useState('');
	const [locationLabel, setLocationLabel] = useState('');
	const [saveStatus, setSaveStatus] = useState<
		'idle' | 'saving' | 'saved' | 'error'
	>('idle');

	// Search preferences state
	const [searchRadius, setSearchRadius] = useState<number>(1000);
	const [numCandidates, setNumCandidates] = useState<number>(3);
	const [searchPrefsStatus, setSearchPrefsStatus] = useState<
		'idle' | 'saving' | 'saved' | 'error'
	>('idle');

	// User preferences state
	const [settings, setSettings] = useState<QuickSetting[]>([
		{
			id: 'email-notifications',
			label: 'Email Notifications',
			description: 'Receive updates and recommendations via email',
			icon: <FiBell className='w-5 h-5' />,
			enabled: true,
			category: 'notifications',
		},
		{
			id: 'location-history',
			label: 'Location History',
			description: 'Save visited places for personalized suggestions',
			icon: <FiMapPin className='w-5 h-5' />,
			enabled: true,
			category: 'privacy',
		},
		{
			id: 'activity-status',
			label: 'Activity Status',
			description: "Show when you're active on the platform",
			icon: <FiEye className='w-5 h-5' />,
			enabled: false,
			category: 'privacy',
		},
		{
			id: 'data-usage-improvement',
			label: 'Data Usage for App Improvement',
			description:
				'Allow us to use your chat interactions (anonymously) to improve our AI',
			icon: <FiSettings className='w-5 h-5' />,
			enabled: false,
			category: 'privacy',
		},
	]);

	// Helper functions for preferences
	const toggleSetting = (settingId: string) => {
		setSettings((prev) =>
			prev.map((setting) =>
				setting.id === settingId
					? { ...setting, enabled: !setting.enabled }
					: setting
			)
		);
	};

	const getCategoryIcon = (category: string) => {
		switch (category) {
			case 'location':
				return <FiMapPin className='w-5 h-5 text-emerald-600' />;
			case 'notifications':
				return <FiBell className='w-5 h-5 text-blue-600' />;
			case 'privacy':
				return <FiEye className='w-5 h-5 text-purple-600' />;
			case 'interface':
				return <FiSettings className='w-5 h-5 text-orange-600' />;
			default:
				return <FiSettings className='w-5 h-5 text-gray-600' />;
		}
	};

	const ToggleSwitch: React.FC<{
		checked: boolean;
		onChange: (checked: boolean) => void;
	}> = ({ checked, onChange }) => (
		<button
			onClick={() => onChange(!checked)}
			className='relative inline-flex h-6 w-11 items-center rounded-full transition-colors'
			style={{
				backgroundColor: checked
					? colors.supporting.lightBlue
					: colors.ui.gray300,
			}}>
			<span
				className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
					checked ? 'translate-x-6' : 'translate-x-1'
				}`}
			/>
		</button>
	);

	// Load current location into form when component mounts
	useEffect(() => {
		if (userLocation && userLocation.source === 'manual') {
			setManualLat(userLocation.latitude.toString());
			setManualLng(userLocation.longitude.toString());
			setLocationLabel(userLocation.label || '');
		}
	}, [userLocation]);

	// Load search preferences from database when component mounts
	useEffect(() => {
		const loadSearchPreferences = async () => {
			try {
				// Get user ID from auth context or wherever it's stored
				const userId = session?.user.id; // Assuming user is available from session
				if (!userId) return;

				const response = await fetch(
					`/api/user/location-settings?userId=${userId}`
				);
				if (response.ok) {
					const data = await response.json();
					if (data.success && data.settings) {
						setSearchRadius(data.settings.search_radius || 1000);
						setNumCandidates(data.settings.num_candidates || 3);
					}
				}
			} catch (error) {
				console.warn('Failed to load search preferences:', error);
			}
		};

		loadSearchPreferences();
	}, [session]);

	const handleSaveLocation = () => {
		const lat = parseFloat(manualLat);
		const lng = parseFloat(manualLng);

		if (isNaN(lat) || isNaN(lng)) {
			setSaveStatus('error');
			setTimeout(() => setSaveStatus('idle'), 3000);
			return;
		}

		if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
			setSaveStatus('error');
			setTimeout(() => setSaveStatus('idle'), 3000);
			return;
		}

		setSaveStatus('saving');

		try {
			setManualLocation(
				lat,
				lng,
				locationLabel || `${lat.toFixed(4)}, ${lng.toFixed(4)}`
			);
			setSaveStatus('saved');

			// If there's a return URL, redirect after a short delay
			if (returnUrl !== '/settings') {
				setTimeout(() => {
					router.push(returnUrl);
				}, 1500);
			} else {
				setTimeout(() => setSaveStatus('idle'), 3000);
			}
		} catch {
			setSaveStatus('error');
			setTimeout(() => setSaveStatus('idle'), 3000);
		}
	};

	const handleClearLocation = () => {
		clearLocation();
		setManualLat('');
		setManualLng('');
		setLocationLabel('');
		setSaveStatus('idle');
	};

	const handleSaveSearchPreferences = async () => {
		if (!session?.user.id) return;

		setSearchPrefsStatus('saving');

		try {
			const response = await fetch('/api/user/location-settings', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					userId: session.user.id,
					searchRadius,
					numCandidates,
				}),
			});

			if (response.ok) {
				setSearchPrefsStatus('saved');
				setTimeout(() => setSearchPrefsStatus('idle'), 3000);
			} else {
				setSearchPrefsStatus('error');
				setTimeout(() => setSearchPrefsStatus('idle'), 3000);
			}
		} catch (error) {
			console.error('Failed to save search preferences:', error);
			setSearchPrefsStatus('error');
			setTimeout(() => setSearchPrefsStatus('idle'), 3000);
		}
	};

	const renderLocationTab = () => (
		<div className='space-y-6'>
			<div>
				<h3 className='text-lg font-semibold text-gray-800 mb-4'>
					Location Settings
				</h3>
				<p className='text-gray-600 text-sm mb-6'>
					Set your location manually for location-based features. This will be
					used when automatic location is not available.
				</p>
			</div>

			{/* Current Location Display */}
			{userLocation && (
				<div className='bg-blue-50 border border-blue-200 p-4'>
					<div className='flex items-start gap-3'>
						<FaMapMarkerAlt className='text-blue-600 mt-1' />
						<div className='flex-1'>
							<h4 className='font-medium text-blue-800'>Current Location</h4>
							<p className='text-blue-700 text-sm'>
								{userLocation.label} ({userLocation.latitude.toFixed(4)},{' '}
								{userLocation.longitude.toFixed(4)})
							</p>
							<p className='text-blue-600 text-xs mt-1'>
								Source:{' '}
								{userLocation.source === 'auto'
									? 'Automatic (GPS)'
									: 'Manual Entry'}
								{userLocation.source === 'auto' && (
									<span className='ml-2'>
										• Live Updates:{' '}
										{userLocation.liveLocationEnabled ? 'ON' : 'OFF'}
									</span>
								)}
							</p>

							{/* Live Location Toggle for Auto Location */}
							{userLocation.source === 'auto' && (
								<div className='mt-3 flex items-center gap-3'>
									<label className='flex items-center gap-2 cursor-pointer'>
										<input
											type='checkbox'
											checked={userLocation.liveLocationEnabled ?? true}
											onChange={(e) => toggleLiveLocation(e.target.checked)}
											className='w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500'
										/>
										<span className='text-sm text-blue-700'>
											Enable live location updates (every 1 minute)
										</span>
									</label>
								</div>
							)}
						</div>
					</div>
				</div>
			)}

			{/* Manual Location Entry */}
			<div className='bg-white border border-gray-200 p-6'>
				<div className='flex items-center justify-between mb-4'>
					<h4 className='font-medium text-gray-800'>Manual Location Entry</h4>
					{userLocation?.source === 'auto' &&
						userLocation?.liveLocationEnabled && (
							<span className='text-sm text-amber-600 bg-amber-50 px-2 py-1 rounded'>
								Disabled while live location is active
							</span>
						)}
				</div>

				<div className='space-y-4'>
					<div>
						<label className='block text-sm font-medium text-gray-700 mb-2'>
							Location Label (Optional)
						</label>
						<input
							type='text'
							value={locationLabel}
							onChange={(e) => setLocationLabel(e.target.value)}
							placeholder='e.g., Home, Office, New York'
							disabled={
								userLocation?.source === 'auto' &&
								userLocation?.liveLocationEnabled
							}
							className='w-full px-3 py-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
						/>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						<div>
							<label className='block text-sm font-medium text-gray-700 mb-2'>
								Latitude
							</label>
							<input
								type='number'
								step='any'
								value={manualLat}
								onChange={(e) => setManualLat(e.target.value)}
								placeholder='e.g., 40.7128'
								min='-90'
								max='90'
								disabled={
									userLocation?.source === 'auto' &&
									userLocation?.liveLocationEnabled
								}
								className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
							/>
							<p className='text-xs text-gray-500 mt-1'>Range: -90 to 90</p>
						</div>

						<div>
							<label className='block text-sm font-medium text-gray-700 mb-2'>
								Longitude
							</label>
							<input
								type='number'
								step='any'
								value={manualLng}
								onChange={(e) => setManualLng(e.target.value)}
								placeholder='e.g., -74.0060'
								min='-180'
								max='180'
								disabled={
									userLocation?.source === 'auto' &&
									userLocation?.liveLocationEnabled
								}
								className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed'
							/>
							<p className='text-xs text-gray-500 mt-1'>Range: -180 to 180</p>
						</div>
					</div>

					{/* Search Preferences */}
					<div className='border-t pt-4 mt-4'>
						<h5 className='font-medium text-gray-800 mb-3'>
							Search Preferences
						</h5>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Search Radius (meters)
								</label>
								<input
									type='number'
									value={searchRadius || 1000}
									onChange={(e) => setSearchRadius(parseInt(e.target.value))}
									placeholder='1000'
									min='100'
									max='10000'
									step='100'
									className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
								/>
								<p className='text-xs text-gray-500 mt-1'>
									Range: 100-10,000 meters
								</p>
							</div>

							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Number of Results
								</label>
								<input
									type='number'
									value={numCandidates || 3}
									onChange={(e) => setNumCandidates(parseInt(e.target.value))}
									placeholder='3'
									min='1'
									max='20'
									className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
								/>
								<p className='text-xs text-gray-500 mt-1'>
									Range: 1-20 results
								</p>
							</div>
						</div>

						{/* Save Search Preferences Button */}
						<div className='flex gap-3 pt-4'>
							<button
								onClick={handleSaveSearchPreferences}
								disabled={searchPrefsStatus === 'saving'}
								className='flex items-center gap-2 bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-2 rounded-lg transition-colors'>
								<FaSave className='w-4 h-4' />
								{searchPrefsStatus === 'saving'
									? 'Saving...'
									: 'Save Search Preferences'}
							</button>
						</div>

						{searchPrefsStatus === 'saved' && (
							<div className='text-green-600 text-sm'>
								✅ Search preferences saved successfully!
							</div>
						)}
						{searchPrefsStatus === 'error' && (
							<div className='text-red-600 text-sm'>
								❌ Failed to save search preferences. Please try again.
							</div>
						)}
					</div>

					<div className='flex gap-3 pt-4'>
						<button
							onClick={handleSaveLocation}
							disabled={
								!manualLat ||
								!manualLng ||
								saveStatus === 'saving' ||
								(userLocation?.source === 'auto' &&
									userLocation?.liveLocationEnabled)
							}
							className='flex items-center gap-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 transition-colors'>
							<FaSave className='w-4 h-4' />
							{saveStatus === 'saving' ? 'Saving...' : 'Save Location'}
						</button>

						{userLocation && (
							<button
								onClick={handleClearLocation}
								className='flex items-center gap-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 transition-colors'>
								<FaTrash className='w-4 h-4' />
								Clear Location
							</button>
						)}
					</div>

					{saveStatus === 'saved' && (
						<div className='text-green-600 text-sm'>
							✅ Location saved successfully!
						</div>
					)}
					{saveStatus === 'error' && (
						<div className='text-red-600 text-sm'>
							❌ Invalid coordinates. Please check your input.
						</div>
					)}
				</div>
			</div>

			<div className='bg-gray-50 border border-gray-200 rounded-lg p-4'>
				<h5 className='font-medium text-gray-800 mb-2'>Location Options:</h5>
				<ul className='text-sm text-gray-600 space-y-1'>
					<li>
						• <strong>Live Location:</strong> Updates automatically every 1
						minute using GPS
					</li>
					<li>
						• <strong>Manual Location:</strong> Uses fixed coordinates you enter
					</li>
					<li>
						• <strong>No Location:</strong> App works without location data
					</li>
				</ul>
				<h5 className='font-medium text-gray-800 mb-2 mt-4'>
					How to find coordinates:
				</h5>
				<ul className='text-sm text-gray-600 space-y-1'>
					<li>• Open Google Maps and right-click on your desired location</li>
					<li>• The coordinates will appear in the context menu</li>
					<li>
						• You can also search for a place and copy coordinates from the URL
					</li>
				</ul>
			</div>
		</div>
	);

	const renderGeneralTab = () => (
		<div className='space-y-6'>
			<div>
				<h3
					className='text-lg font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					User Preferences
				</h3>
				<p
					className='text-sm mb-6'
					style={{ color: colors.neutral.slateGray }}>
					Customize your experience and privacy settings.
				</p>
			</div>

			{/* User Preferences */}
			<div
				className='rounded-lg p-6'
				style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<div className='flex items-center gap-2 mb-4'>
					<div
						className='w-6 h-6 rounded flex items-center justify-center'
						style={{ backgroundColor: colors.supporting.lightBlue }}>
						<FiSettings className='w-4 h-4 text-white' />
					</div>
					<h4
						className='text-base font-medium'
						style={{ color: colors.neutral.textBlack }}>
						Quick Settings
					</h4>
				</div>

				<div className='space-y-4'>
					{settings.map((setting) => (
						<div
							key={setting.id}
							className='flex items-center justify-between py-3 border-b'
							style={{ borderColor: colors.ui.gray200 }}>
							<div className='flex items-center gap-3'>
								<div
									className='w-8 h-8 flex items-center justify-center'
									style={{ backgroundColor: colors.ui.gray100 }}>
									{getCategoryIcon(setting.category)}
								</div>
								<div>
									<span
										className='text-sm font-medium block'
										style={{ color: colors.neutral.textBlack }}>
										{setting.label}
									</span>
									<p
										className='text-xs leading-relaxed'
										style={{ color: colors.ui.gray500 }}>
										{setting.description}
									</p>
								</div>
							</div>
							<ToggleSwitch
								checked={setting.enabled}
								onChange={() => toggleSetting(setting.id)}
							/>
						</div>
					))}
				</div>

				{/* Quick Actions */}
				<div
					className='mt-6 pt-4'
					style={{ borderTop: `1px solid ${colors.ui.gray200}` }}>
					<button
						onClick={() => router.push('/credits')}
						className='flex items-center gap-2 px-4 py-2 transition-colors text-sm font-medium'
						style={{
							backgroundColor: colors.brand.blue,
							color: 'white',
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor =
								colors.supporting.lightBlue;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.brand.blue;
						}}>
						<FiZap className='w-4 h-4' />
						<span>Manage Credits</span>
					</button>
				</div>
			</div>
		</div>
	);

	const renderLegalTab = () => (
		<div className='space-y-6'>
			<div>
				<h3
					className='text-lg font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					Terms & Conditions
				</h3>
				<p
					className='text-sm mb-6'
					style={{ color: colors.neutral.slateGray }}>
					By creating an account and using Wizlop, you agree to these terms and
					conditions.
				</p>
			</div>

			{/* Terms of Service */}
			<div
				className='rounded-lg p-6'
				style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<h4
					className='text-base font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					1. Terms of Service
				</h4>
				<div
					className='space-y-4 text-sm'
					style={{ color: colors.neutral.slateGray }}>
					<div>
						<p className='font-medium mb-2'>1.1 Service Description</p>
						<p>
							Wizlop is an AI-powered travel assistant that helps you discover
							locations and provides personalized travel recommendations. By
							using our service, you agree to these terms.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>1.2 Credit System</p>
						<p>
							Our service operates on a credit-based system. 1 credit allows you
							to send up to 5 messages to our AI. Credits can be purchased,
							earned through contributions, or obtained via subscription plans.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>1.3 User Responsibilities</p>
						<p>
							Users must provide accurate information, use the service lawfully,
							respect intellectual property rights, and not attempt to reverse
							engineer or misuse our AI technology.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>1.4 Service Availability</p>
						<p>
							We strive for 99.9% uptime but cannot guarantee uninterrupted
							service. We reserve the right to modify or discontinue features
							with reasonable notice.
						</p>
					</div>
				</div>
			</div>

			{/* Privacy Policy */}
			<div
				className='rounded-lg p-6'
				style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<h4
					className='text-base font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					2. Privacy Policy
				</h4>
				<div
					className='space-y-4 text-sm'
					style={{ color: colors.neutral.slateGray }}>
					<div>
						<p className='font-medium mb-2'>2.1 Information We Collect</p>
						<p>
							We collect your email address, location data (when provided), chat
							interactions, and usage analytics to provide and improve our
							service.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>2.2 Location Data</p>
						<p>
							Location data is collected only with your explicit permission and
							is used solely for providing personalized travel recommendations.
							You can disable location sharing at any time.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>2.3 Chat Interactions</p>
						<p>
							Your conversations are stored to provide context for future
							interactions and improve our AI. You can delete your chat history
							anytime from your profile.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>2.4 Data Security</p>
						<p>
							We use industry-standard encryption (AES-256) to protect your
							data. Your personal information is never shared with third parties
							without your explicit consent.
						</p>
					</div>
				</div>
			</div>

			{/* Data Usage for Improvement */}
			<div
				className='rounded-lg p-6'
				style={{ backgroundColor: colors.ui.green50 }}>
				<h4
					className='text-base font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					3. Data Usage for App Improvement
				</h4>
				<div
					className='space-y-4 text-sm'
					style={{ color: colors.neutral.slateGray }}>
					<div>
						<p className='font-medium mb-2'>3.1 Anonymous Data Training</p>
						<p>
							With your permission, we may use your chat interactions
							(completely anonymized and disconnected from your profile) to
							train and improve our AI models.
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>3.2 What We Keep vs. What We Use</p>
						<p>
							<strong>We Keep:</strong> Your profile information, location
							preferences, and chat history linked to your account for your
							personal use.
						</p>
						<p className='mt-2'>
							<strong>We Use for Training:</strong> Only anonymized conversation
							patterns and travel preferences (no personal identifiers,
							locations, or profile data).
						</p>
					</div>
					<div>
						<p className='font-medium mb-2'>3.3 Your Control</p>
						<p>
							You can opt out of data usage for improvement at any time in your
							settings. This will not affect your ability to use Wizlop.
						</p>
					</div>
				</div>
			</div>

			{/* User Rights */}
			<div
				className='rounded-lg p-6'
				style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<h4
					className='text-base font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					4. Your Rights
				</h4>
				<div
					className='space-y-3 text-sm'
					style={{ color: colors.neutral.slateGray }}>
					<p>
						• <strong>Access:</strong> Request a copy of your personal data
					</p>
					<p>
						• <strong>Correction:</strong> Update or correct your information
					</p>
					<p>
						• <strong>Deletion:</strong> Request deletion of your account and
						data
					</p>
					<p>
						• <strong>Portability:</strong> Export your chat history and
						preferences
					</p>
					<p>
						• <strong>Opt-out:</strong> Disable data usage for AI improvement
					</p>
				</div>
			</div>

			{/* Contact Information */}
			<div
				className='rounded-lg p-6'
				style={{ backgroundColor: colors.ui.blue50 }}>
				<h4
					className='text-base font-semibold mb-4'
					style={{ color: colors.neutral.textBlack }}>
					5. Contact & Support
				</h4>
				<div
					className='space-y-2 text-sm'
					style={{ color: colors.neutral.slateGray }}>
					<p>
						<strong>Questions about our terms or privacy?</strong> Contact our
						support team.
					</p>
					<p>
						<strong>Email:</strong> <EMAIL>
					</p>
					<p>
						<strong>Support:</strong> <EMAIL>
					</p>
					<p>
						<strong>Last Updated:</strong> December 15, 2024
					</p>
					<p className='mt-4 font-medium'>
						By using Wizlop, you acknowledge that you have read, understood, and
						agree to these terms.
					</p>
				</div>
			</div>
		</div>
	);

	return (
		<div className='min-h-screen'>
			<div className='max-w-4xl mx-auto py-8 px-4'>
				{/* Tab Navigation */}
				<div
					className='border-b mb-8'
					style={{ borderColor: colors.ui.gray200 }}>
					<nav className='flex space-x-8'>
						<button
							onClick={() => router.push('/settings?tab=general')}
							className='py-2 px-1 border-b-2 font-medium text-sm transition-colors'
							style={{
								borderColor:
									activeTab === 'general'
										? colors.supporting.lightBlue
										: 'transparent',
								color:
									activeTab === 'general'
										? colors.supporting.lightBlue
										: colors.neutral.slateGray,
							}}
							onMouseEnter={(e) => {
								if (activeTab !== 'general') {
									e.currentTarget.style.color = colors.neutral.textBlack;
								}
							}}
							onMouseLeave={(e) => {
								if (activeTab !== 'general') {
									e.currentTarget.style.color = colors.neutral.slateGray;
								}
							}}>
							General
						</button>
						<button
							onClick={() => router.push('/settings?tab=location')}
							className='py-2 px-1 border-b-2 font-medium text-sm transition-colors'
							style={{
								borderColor:
									activeTab === 'location'
										? colors.supporting.lightBlue
										: 'transparent',
								color:
									activeTab === 'location'
										? colors.supporting.lightBlue
										: colors.neutral.slateGray,
							}}
							onMouseEnter={(e) => {
								if (activeTab !== 'location') {
									e.currentTarget.style.color = colors.neutral.textBlack;
								}
							}}
							onMouseLeave={(e) => {
								if (activeTab !== 'location') {
									e.currentTarget.style.color = colors.neutral.slateGray;
								}
							}}>
							Location
						</button>
						<button
							onClick={() => router.push('/settings?tab=legal')}
							className='py-2 px-1 border-b-2 font-medium text-sm transition-colors'
							style={{
								borderColor:
									activeTab === 'legal'
										? colors.supporting.lightBlue
										: 'transparent',
								color:
									activeTab === 'legal'
										? colors.supporting.lightBlue
										: colors.neutral.slateGray,
							}}
							onMouseEnter={(e) => {
								if (activeTab !== 'legal') {
									e.currentTarget.style.color = colors.neutral.textBlack;
								}
							}}
							onMouseLeave={(e) => {
								if (activeTab !== 'legal') {
									e.currentTarget.style.color = colors.neutral.slateGray;
								}
							}}>
							Legal
						</button>
					</nav>
				</div>

				{/* Tab Content */}
				<div
					className='shadow-sm border p-6'
					style={{
						backgroundColor: 'white',
						borderColor: colors.ui.gray200,
					}}>
					{activeTab === 'general' && renderGeneralTab()}
					{activeTab === 'location' && renderLocationTab()}
					{activeTab === 'legal' && renderLegalTab()}
				</div>
			</div>
		</div>
	);
}

const SettingsPage: React.FC = () => {
	return (
		<Suspense
			fallback={
				<div
					className='min-h-screen flex items-center justify-center'
					style={{
						background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
					}}>
					<div className='text-center'>
						<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4'></div>
						<p style={{ color: colors.neutral.slateGray }}>
							Loading settings...
						</p>
					</div>
				</div>
			}>
			<SettingsPageContent />
		</Suspense>
	);
};

export default SettingsPage;
