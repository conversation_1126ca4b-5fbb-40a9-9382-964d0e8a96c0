/** @format */

'use client';

import { colors } from '@/app/colors';
import { POIProfileComponent } from '@/app/pois/components';
import { LoadingSpinner } from '@/app/shared/system';
import { Review, ReviewDisplay } from '@/app/shared/userInteractions/reviews';
import { InteractionProvider } from '@/app/shared/userInteractions/shared/context';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FaEdit, FaTimes, FaTrash } from 'react-icons/fa';

interface POI {
	poi_type: string;
	poi_id: number | null;
	temp_id: number | null;
	approved_id: number | null;
	name?: string;
	profile_picture_url?: string;
	name_en?: string;
	name_tr?: string;
	name_uk?: string;
	name_de?: string;
	name_ru?: string;
	name_ar?: string;
	category?: string;
	subcategory?: string;
	cuisine?: string;
	latitude?: number;
	longitude?: number;
	city?: string;
	district?: string;
	neighborhood?: string;
	street?: string;
	full_address?: string;
	province?: string;
	phone_number?: string;
	opening_hours?: string;
	is_favorite?: boolean;
	description?: string;
	democratic_categories?: string[];
	community_tags?: string[];
	legacy_categories?: Record<string, unknown>;
	rating?: number;
	reviews_count?: number;
	user_rating_count?: number;
	online_rating_avg?: number;
	online_rating_count?: number;
	online_rating_source?: string;
	online_reviews?: Record<string, unknown>;
	last_online_update?: string;
	view_count?: number;
	favorite_count?: number;
	visit_count?: number;
	share_count?: number;
	like_count?: number;
	popularity_score?: number;
	trending_score?: number;
	price_range?: string;
	reservation_required?: boolean;
	accessibility_rating?: number;
	status?: string;
	verification_level?: number;
	promoted_at?: string;
	last_verified_at?: string;
	created_at?: string;
	created_by?: string;
	media_count?: number;
}

interface ReportForm {
	name?: string;
	name_en?: string;
	name_tr?: string;
	name_uk?: string;
	name_de?: string;
	name_ru?: string;
	name_ar?: string;
	category?: string;
	subcategory?: string;
	cuisine?: string;
	full_address?: string;
	street?: string;
	neighborhood?: string;
	district?: string;
	city?: string;
	province?: string;
	phone_number?: string;
	opening_hours?: string;
	description?: string;
	latitude?: string;
	longitude?: string;
	closed_description?: string;
}

const POIProfilePage: React.FC = () => {
	const router = useRouter();
	const params = useParams();
	const { data: session } = useSession();
	const [poi, setPoi] = useState<POI | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showReviewsOverlay, setShowReviewsOverlay] = useState(false);
	const [refreshReviews, setRefreshReviews] = useState(0);
	const [reviewRating, setReviewRating] = useState(0);
	const [reviewText, setReviewText] = useState('');
	const [visitDate, setVisitDate] = useState('');
	const [hoveredRating, setHoveredRating] = useState(0);
	const [reviewSubmitting, setReviewSubmitting] = useState(false);
	const [reviewSuccess, setReviewSuccess] = useState<string | null>(null);
	const [reviewError, setReviewError] = useState<string | null>(null);
	const [showReportModal, setShowReportModal] = useState(false);
	const [reportType, setReportType] = useState<'info_update' | 'closed' | null>(
		null
	);
	const [reportForm, setReportForm] = useState<ReportForm>({});
	const [reportSubmitting, setReportSubmitting] = useState(false);
	const [reportSuccess, setReportSuccess] = useState<string | null>(null);
	const [reportError, setReportError] = useState<string | null>(null);

	const poiType = params.poiType as string;
	const poiId = params.poiId as string;

	useEffect(() => {
		loadPOI();
	}, [poiType, poiId]);

	useEffect(() => {
		if (showReportModal && reportType === 'info_update' && poi) {
			setReportForm({
				name: poi.name || '',
				name_en: poi.name_en || '',
				name_tr: poi.name_tr || '',
				name_uk: poi.name_uk || '',
				name_de: poi.name_de || '',
				name_ru: poi.name_ru || '',
				name_ar: poi.name_ar || '',
				category: poi.category || '',
				subcategory: poi.subcategory || '',
				cuisine: poi.cuisine || '',
				full_address: poi.full_address || '',
				street: poi.street || '',
				neighborhood: poi.neighborhood || '',
				district: poi.district || '',
				city: poi.city || '',
				province: poi.province || '',
				phone_number: poi.phone_number || '',
				opening_hours: poi.opening_hours || '',
				description: poi.description || '',
				latitude: poi.latitude?.toString() || '',
				longitude: poi.longitude?.toString() || '',
			});
		}
		if (!showReportModal || reportType !== 'info_update') {
			setReportForm({});
		}
	}, [showReportModal, reportType, poi]);

	const loadPOI = async () => {
		setLoading(true);
		setError(null);

		try {
			const response = await fetch(`/api/pois/${poiType}/${poiId}`);
			const data = await response.json();

			if (data.success) {
				setPoi(data.poi);
			} else {
				setError(data.error || 'Failed to load POI');
			}
		} catch (error) {
			console.error('Failed to load POI:', error);
			setError('Failed to load POI');
		} finally {
			setLoading(false);
		}
	};

	// Interaction counts are now managed by the UserInteractionButtons component hooks

	const handleShowReviews = () => {
		setShowReviewsOverlay(true);
	};

	const handleCloseReviews = () => {
		setShowReviewsOverlay(false);
		// Reset form
		setReviewRating(0);
		setReviewText('');
		setVisitDate('');
		setHoveredRating(0);
		// Clear messages
		setReviewSuccess(null);
		setReviewError(null);
	};

	const formatDisplayDate = (dateString: string) => {
		if (!dateString) return '';
		const date = new Date(dateString);
		return date.toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			year: 'numeric',
		});
	};

	const handleEditReview = (review: Review) => {
		// Handle edit review in the overlay
		console.log('Edit review:', review);
	};

	const handleRatingClick = (rating: number) => {
		setReviewRating(rating);
	};

	const handleSubmitReview = async () => {
		// Clear previous messages
		setReviewSuccess(null);
		setReviewError(null);

		if (!reviewRating || !reviewText.trim()) {
			setReviewError('Please provide both a rating and a review comment.');
			return;
		}

		if (!poi) {
			setReviewError('POI information not available.');
			return;
		}

		setReviewSubmitting(true);

		try {
			const response = await fetch('/api/pois/reviews', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					rating: reviewRating,
					review_text: reviewText,
					visit_date: visitDate || null,
					poiId: poi.poi_id,
					poiType: poiType,
					userPoiTempId: poi.temp_id,
					userPoiApprovedId: poi.approved_id,
				}),
			});

			if (response.ok) {
				// Reset form
				setReviewRating(0);
				setReviewText('');
				setVisitDate('');
				setRefreshReviews((prev) => prev + 1);
				setReviewSuccess('Review submitted successfully!');

				// Auto-hide success message after 3 seconds
				setTimeout(() => setReviewSuccess(null), 3000);
			} else {
				const errorData = await response.json();
				setReviewError(
					errorData.error || 'Failed to submit review. Please try again.'
				);
			}
		} catch (error) {
			console.error('Error submitting review:', error);
			setReviewError('Failed to submit review. Please try again.');
		} finally {
			setReviewSubmitting(false);
		}
	};

	const isReviewValid = reviewRating > 0 && reviewText.trim().length > 0;

	const getBestPOIName = (poi: POI) => {
		return (
			poi.name ||
			poi.name_en ||
			poi.name_tr ||
			poi.name_uk ||
			poi.name_de ||
			poi.name_ru ||
			poi.name_ar ||
			'Unnamed Location'
		);
	};

	// Report submit handler
	const handleReportSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setReportSubmitting(true);
		setReportError(null);
		setReportSuccess(null);
		try {
			const res = await fetch('/api/pois/report', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					poiType,
					poiId,
					reportType,
					...reportForm,
					originalPoiId: poi && poi.poi_id ? Number(poi.poi_id) : undefined,
				}),
			});
			const data = await res.json();
			if (data.success) {
				setReportSuccess('Report submitted successfully!');
				setShowReportModal(false);
				setReportType(null);
				setReportForm({});
			} else {
				setReportError(data.error || 'Failed to submit report');
			}
		} catch {
			setReportError('Failed to submit report');
		} finally {
			setReportSubmitting(false);
		}
	};

	if (loading) {
		return (
			<div className='min-h-screen bg-gray-50 flex items-center justify-center'>
				<LoadingSpinner />
			</div>
		);
	}

	if (error || !poi) {
		return (
			<div className='min-h-screen bg-gray-50 flex items-center justify-center'>
				<div className='text-center'>
					<div className='text-6xl mb-4'>❌</div>
					<h3
						className='text-xl font-semibold mb-2'
						style={{ color: colors.neutral.textBlack }}>
						{error || 'POI not found'}
					</h3>
					<button
						onClick={() => router.back()}
						className='px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
						Go Back
					</button>
				</div>
			</div>
		);
	}

	return (
		<InteractionProvider>
			<div className='min-h-screen'>
				{/* Main Content */}
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
					{/* Enhanced POI Profile Section - Now includes hero functionality */}
					<div className='mb-8'>
						<POIProfileComponent
							poi={{
								poi_id:
									poi.poi_id ||
									(poi.temp_id ? Number(poi.temp_id) : null) ||
									(poi.approved_id ? Number(poi.approved_id) : null) ||
									0,
								poi_type: poi.poi_type,
								name: getBestPOIName(poi),
								category: poi.category,
								subcategory: poi.subcategory,
								description: poi.description,
								full_address: poi.full_address,
								phone_number: poi.phone_number,
								opening_hours: poi.opening_hours,
								rating: poi.rating,
								reviews_count: poi.reviews_count,
								media_count: poi.media_count,
								visit_count: poi.visit_count,
								like_count: poi.like_count,
								favorite_count: poi.favorite_count,
								created_by: poi.created_by,
								created_at: poi.created_at,
							}}
							isOwner={session?.user?.id === poi.created_by}
							onReportClick={() => setShowReportModal(true)}
							onWriteReview={handleShowReviews}
						/>
					</div>
				</div>

				{/* Unified Reviews Overlay Modal */}
				{showReviewsOverlay && (
					<div className='fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm'>
						<div className='bg-white rounded-3xl shadow-2xl max-w-5xl w-full mx-4 max-h-[95vh] overflow-hidden'>
							{/* Header */}
							<div className='relative bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6'>
								<div className='flex items-center justify-between'>
									<div>
										<h2 className='text-2xl font-bold mb-1'>
											Reviews & Ratings
										</h2>
										<p className='text-blue-100 text-sm'>
											{getBestPOIName(poi)}
										</p>
									</div>
									<button
										onClick={handleCloseReviews}
										className='p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors'>
										<FaTimes className='w-5 h-5' />
									</button>
								</div>
							</div>

							{/* Content Area */}
							<div className='flex h-[calc(95vh-120px)]'>
								{/* Left Side - Reviews List */}
								<div
									className='flex-1 p-6 overflow-y-auto border-r'
									style={{ borderColor: colors.ui.gray200 }}>
									<div className='mb-6'>
										<div className='flex items-center justify-between mb-4'>
											<h3
												className='text-lg font-semibold'
												style={{ color: colors.neutral.textBlack }}>
												Community Reviews
											</h3>
											<div className='flex items-center gap-2 text-sm text-gray-500'>
												<span>Sort by:</span>
												<select className='border rounded-lg px-2 py-1 text-sm'>
													<option>Most Recent</option>
													<option>Highest Rated</option>
													<option>Most Helpful</option>
												</select>
											</div>
										</div>

										{/* Rating Summary */}
										{poi.rating && (
											<div className='bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-6'>
												<div className='flex items-center gap-4'>
													<div className='text-center'>
														<div className='text-3xl font-bold text-blue-600'>
															{poi.rating.toFixed(1)}
														</div>
														<div className='flex items-center justify-center gap-1 mt-1'>
															{[1, 2, 3, 4, 5].map((star) => (
																<div
																	key={star}
																	className={`w-4 h-4 ${
																		star <= Math.round(poi.rating || 0)
																			? 'text-yellow-400'
																			: 'text-gray-300'
																	}`}>
																	⭐
																</div>
															))}
														</div>
														<div className='text-xs text-gray-500 mt-1'>
															{poi.reviews_count || 0} reviews
														</div>
													</div>
													<div className='flex-1'>
														<div className='text-sm text-gray-600 mb-2'>
															Rating Distribution
														</div>
														{[5, 4, 3, 2, 1].map((rating) => (
															<div
																key={rating}
																className='flex items-center gap-2 mb-1'>
																<span className='text-xs w-3'>{rating}</span>
																<div className='flex-1 bg-gray-200 rounded-full h-2'>
																	<div
																		className='bg-yellow-400 h-2 rounded-full'
																		style={{
																			width: `${Math.random() * 80 + 10}%`,
																		}}></div>
																</div>
															</div>
														))}
													</div>
												</div>
											</div>
										)}
									</div>

									<ReviewDisplay
										poiId={poi.poi_id}
										userPoiTempId={poi.temp_id}
										userPoiApprovedId={poi.approved_id}
										poiType={poi.poi_type}
										refreshTrigger={refreshReviews}
										onEditReview={handleEditReview}
									/>
								</div>

								{/* Right Side - Write Review */}
								<div className='w-96 p-6 bg-gray-50'>
									<div className='sticky top-0'>
										<h3
											className='text-lg font-semibold mb-4'
											style={{ color: colors.neutral.textBlack }}>
											Share Your Experience
										</h3>

										{session?.user ? (
											<div className='space-y-4'>
												{/* User Info */}
												<div className='flex items-center gap-3 p-3 bg-white rounded-lg'>
													<div className='w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold'>
														{session.user.name?.charAt(0) || 'U'}
													</div>
													<div>
														<div className='font-medium text-sm'>
															{session.user.name}
														</div>
														<div className='text-xs text-gray-500'>
															Writing as {session.user.name}
														</div>
													</div>
												</div>

												{/* Rating Input with Date */}
												<div className='bg-white rounded-lg p-4'>
													<label className='block text-sm font-medium mb-2'>
														Your Rating <span className='text-red-500'>*</span>
													</label>
													<div className='flex items-center justify-between mb-4'>
														{/* Stars */}
														<div className='flex gap-1.5'>
															{[1, 2, 3, 4, 5].map((star) => {
																const isActive =
																	star <= (hoveredRating || reviewRating);
																return (
																	<button
																		key={star}
																		onClick={() => handleRatingClick(star)}
																		onMouseEnter={() => setHoveredRating(star)}
																		onMouseLeave={() => setHoveredRating(0)}
																		className='text-3xl hover:scale-110 transition-all duration-200 focus:outline-none'
																		type='button'>
																		{isActive ? (
																			<span className='text-yellow-400 drop-shadow-sm'>
																				★
																			</span>
																		) : (
																			<span className='text-gray-300'>☆</span>
																		)}
																	</button>
																);
															})}
														</div>

														{/* Calendar Section */}
														<div className='flex items-center gap-3 min-w-0'>
															<div className='relative flex-shrink-0'>
																<input
																	type='date'
																	value={visitDate}
																	onChange={(e) => setVisitDate(e.target.value)}
																	className='opacity-0 absolute inset-0 w-full h-full cursor-pointer z-10'
																	max={new Date().toISOString().split('T')[0]}
																/>
																<div
																	className={`w-10 h-10 flex items-center justify-center rounded-xl transition-all duration-300 hover:bg-blue-50 hover:scale-105 ${
																		visitDate
																			? 'text-blue-600 bg-blue-50 transform translate-x-3 shadow-sm'
																			: 'text-gray-400 hover:text-gray-600'
																	}`}
																	title='Select visit date'>
																	📅
																</div>
															</div>

															{visitDate && (
																<div className='flex-1 min-w-0'>
																	<span className='text-sm text-blue-600 font-semibold bg-blue-50 px-3 py-1.5 rounded-lg transition-all duration-300 whitespace-nowrap'>
																		{formatDisplayDate(visitDate)}
																	</span>
																</div>
															)}
														</div>
													</div>
													<div className='text-xs text-gray-500'>
														Click to rate this place
													</div>
												</div>

												{/* Review Text */}
												<div className='bg-white rounded-lg p-4'>
													<label className='block text-sm font-medium mb-2'>
														Your Review <span className='text-red-500'>*</span>
													</label>
													<textarea
														value={reviewText}
														onChange={(e) => setReviewText(e.target.value)}
														className='w-full h-32 p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
														placeholder='Share your experience at this place...'
														minLength={10}
													/>
													<div className='text-xs text-gray-500 mt-2'>
														Help others by sharing what you liked or didn't like
														(minimum 10 characters)
													</div>
												</div>

												{/* Success/Error Messages */}
												{reviewSuccess && (
													<div className='bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center gap-2'>
														<span className='text-green-500'>✓</span>
														{reviewSuccess}
													</div>
												)}
												{reviewError && (
													<div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center gap-2'>
														<span className='text-red-500'>⚠</span>
														{reviewError}
													</div>
												)}

												{/* Submit Button */}
												<button
													onClick={handleSubmitReview}
													disabled={!isReviewValid || reviewSubmitting}
													className={`w-full py-3 rounded-lg font-medium transition-all transform ${
														isReviewValid && !reviewSubmitting
															? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 hover:scale-[1.02] cursor-pointer'
															: 'bg-gray-300 text-gray-500 cursor-not-allowed'
													}`}>
													{reviewSubmitting ? (
														<div className='flex items-center justify-center gap-2'>
															<div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
															Submitting...
														</div>
													) : isReviewValid ? (
														'Publish Review'
													) : (
														'Please complete rating and review'
													)}
												</button>
											</div>
										) : (
											<div className='text-center py-8'>
												<div className='text-gray-400 mb-4 text-4xl'>👤</div>
												<h4 className='font-medium mb-2'>
													Sign in to write a review
												</h4>
												<p className='text-sm text-gray-500 mb-4'>
													Share your experience and help others discover great
													places
												</p>
												<button
													onClick={() => router.push('/auth?mode=signin')}
													className='bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors'>
													Sign In
												</button>
											</div>
										)}
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Report Modal */}
				{showReportModal && (
					<div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm'>
						<div className='bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 max-h-[90vh] overflow-hidden'>
							<div
								className='flex items-center justify-between p-6 border-b'
								style={{ borderColor: colors.ui.gray200 }}>
								<div>
									<h2
										className='text-xl font-bold'
										style={{ color: colors.neutral.textBlack }}>
										Submit Report
									</h2>
									<p
										className='text-sm'
										style={{ color: colors.neutral.slateGray }}>
										Help us keep this POI accurate
									</p>
								</div>
								<button
									onClick={() => {
										setShowReportModal(false);
										setReportType(null);
										setReportForm({});
									}}
									className='p-2 rounded-lg transition-colors hover:bg-gray-100'>
									<FaTimes
										className='w-5 h-5'
										style={{ color: colors.neutral.slateGray }}
									/>
								</button>
							</div>
							<div className='p-6 overflow-y-auto max-h-[calc(90vh-140px)]'>
								{!reportType ? (
									<div className='flex flex-col gap-4'>
										<button
											onClick={() => setReportType('info_update')}
											className='w-full flex items-center gap-3 px-4 py-3 rounded-lg bg-blue-100 text-blue-800 font-semibold hover:bg-blue-200 transition-colors'>
											<FaEdit className='w-5 h-5' /> Location Info Update
										</button>
										<button
											onClick={() => setReportType('closed')}
											className='w-full flex items-center gap-3 px-4 py-3 rounded-lg bg-red-100 text-red-800 font-semibold hover:bg-red-200 transition-colors'>
											<FaTrash className='w-5 h-5' /> Location Closed / Not
											Found
										</button>
									</div>
								) : (
									<form
										onSubmit={handleReportSubmit}
										className='space-y-6'>
										{reportType === 'info_update' && (
											<>
												<div className='grid grid-cols-1 gap-4'>
													<label className='block text-sm font-medium'>
														POI Name
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name: e.target.value,
															}))
														}
													/>
													<label className='block text-xs font-medium mt-2'>
														Name (EN)
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name_en || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name_en: e.target.value,
															}))
														}
													/>
													<label className='block text-xs font-medium mt-2'>
														Name (TR)
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name_tr || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name_tr: e.target.value,
															}))
														}
													/>
													<label className='block text-xs font-medium mt-2'>
														Name (UK)
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name_uk || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name_uk: e.target.value,
															}))
														}
													/>
													<label className='block text-xs font-medium mt-2'>
														Name (DE)
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name_de || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name_de: e.target.value,
															}))
														}
													/>
													<label className='block text-xs font-medium mt-2'>
														Name (RU)
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name_ru || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name_ru: e.target.value,
															}))
														}
													/>
													<label className='block text-xs font-medium mt-2'>
														Name (AR)
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.name_ar || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																name_ar: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Category
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.category || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																category: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Subcategory
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.subcategory || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																subcategory: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Cuisine
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.cuisine || ''}
														onChange={(e) =>
															setReportForm((f) => ({
																...f,
																cuisine: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Full Address
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.full_address || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																full_address: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Street
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.street || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																street: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Neighborhood
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.neighborhood || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																neighborhood: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														District
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.district || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																district: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														City
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.city || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																city: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Province
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.province || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																province: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Phone Number
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.phone_number || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																phone_number: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Opening Hours
													</label>
													<input
														className='w-full border rounded-lg p-2'
														value={reportForm.opening_hours || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																opening_hours: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Description
													</label>
													<textarea
														className='w-full border rounded-lg p-2 min-h-[60px]'
														value={reportForm.description || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																description: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Latitude
													</label>
													<input
														className='w-full border rounded-lg p-2'
														type='number'
														step='any'
														value={reportForm.latitude || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																latitude: e.target.value,
															}))
														}
													/>
													<label className='block text-sm font-medium mt-2'>
														Longitude
													</label>
													<input
														className='w-full border rounded-lg p-2'
														type='number'
														step='any'
														value={reportForm.longitude || ''}
														onChange={(e) =>
															setReportForm((f: ReportForm) => ({
																...f,
																longitude: e.target.value,
															}))
														}
													/>
												</div>
											</>
										)}
										{reportType === 'closed' && (
											<div>
												<label className='block text-sm font-medium mb-2'>
													Describe the issue *
												</label>
												<textarea
													className='w-full border rounded-lg p-2 min-h-[80px]'
													required
													value={reportForm.closed_description || ''}
													onChange={(e) =>
														setReportForm((f: ReportForm) => ({
															...f,
															closed_description: e.target.value,
														}))
													}
													placeholder='E.g. This place is permanently closed, moved, or cannot be found.'
												/>
											</div>
										)}
										{reportError && (
											<div className='text-red-600 text-sm'>{reportError}</div>
										)}
										{reportSuccess && (
											<div className='text-green-600 text-sm'>
												{reportSuccess}
											</div>
										)}
										<div className='flex gap-2'>
											<button
												type='button'
												onClick={() => setReportType(null)}
												className='px-4 py-2 rounded-lg border bg-gray-100 hover:bg-gray-200'>
												Back
											</button>
											<button
												type='submit'
												disabled={reportSubmitting}
												className='px-4 py-2 rounded-lg bg-blue-600 text-white font-semibold hover:bg-blue-700 disabled:opacity-50'>
												{reportSubmitting ? 'Submitting...' : 'Submit Report'}
											</button>
										</div>
									</form>
								)}
							</div>
						</div>
					</div>
				)}
			</div>
		</InteractionProvider>
	);
};

export default POIProfilePage;
