/** @format */

import { colors } from '@/app/colors';
import { UserProfile } from '@/app/shared/profile';
import { UserInteractionsProvider } from '@/app/shared/userInteractions/shared/context/UserInteractionsProvider';
import { useSession } from 'next-auth/react';
import React from 'react';
import { EnhancedProfileHeader } from './EnhancedProfileHeader';
import MyMediaGallery from './MyMediaGallery';
import { UserInteractionsSection } from './UserInteractionsSection';

const ProfilePageComponent: React.FC = () => {
	const { data: session } = useSession();

	// Handler for profile updates from enhanced components
	const handleProfileUpdate = (updatedProfile: Partial<UserProfile>) => {
		// Profile updates will be handled by the individual components
		console.log('Profile updated:', updatedProfile);
	};

	return (
		<div className='min-h-screen'>
			{session?.user?.id && (
				<UserInteractionsProvider userId={session.user.id}>
					{/* Instagram-style Profile Header - Full Width */}
					<div
						className='w-full border-b'
						style={{ borderColor: colors.ui.gray200 }}>
						<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto py-8 md:py-12'>
							<EnhancedProfileHeader
								user={session?.user}
								onProfileUpdate={handleProfileUpdate}
								integrated={true}
							/>
						</div>
					</div>

					{/* Main Content Area */}
					<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto py-8 md:py-12 lg:py-16'>
						{/* Split Layout - Gallery Bigger */}
						<div className='grid grid-cols-1 xl:grid-cols-5 gap-8 md:gap-12 lg:gap-16'>
							{/* Left Column - Activity (Smaller) */}
							<div className='xl:col-span-2'>
								<div className='space-y-6'>
									<div
										className='pb-4 border-b'
										style={{ borderColor: colors.ui.gray200 }}>
										<h2
											className='text-xl md:text-2xl font-bold'
											style={{ color: colors.neutral.textBlack }}>
											Activity
										</h2>
									</div>
									<UserInteractionsSection
										userId={session.user.id}
										integrated={true}
									/>
								</div>
							</div>

							{/* Right Column - Media Gallery (Bigger) */}
							<div className='xl:col-span-3'>
								<div className='space-y-6'>
									<div
										className='pb-4 border-b'
										style={{ borderColor: colors.ui.gray200 }}>
										<h2
											className='text-xl md:text-2xl font-bold'
											style={{ color: colors.neutral.textBlack }}>
											Gallery
										</h2>
									</div>
									<MyMediaGallery />
								</div>
							</div>
						</div>
					</div>
				</UserInteractionsProvider>
			)}

			{/* Minimalist Fallback for unauthenticated users */}
			{!session?.user?.id && (
				<div className='min-h-screen flex items-center justify-center'>
					<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
						<div className='text-center space-y-8'>
							{/* Simple Icon */}
							<div
								className='w-16 h-16 md:w-20 md:h-20 mx-auto border-2 flex items-center justify-center'
								style={{ borderColor: colors.brand.blue }}>
								<span className='text-2xl md:text-3xl'>👤</span>
							</div>

							{/* Clean Typography */}
							<div className='space-y-4'>
								<h1
									className='text-4xl md:text-5xl lg:text-6xl font-black tracking-tight'
									style={{ color: colors.neutral.textBlack }}>
									Profile
								</h1>
								<p
									className='text-xl md:text-2xl font-light max-w-md mx-auto'
									style={{ color: colors.neutral.slateGray }}>
									Sign in to view your profile
								</p>
							</div>

							{/* Minimal CTA */}
							<div className='pt-8'>
								<button
									className='px-12 py-4 border-2 font-bold text-lg tracking-wide transition-all duration-200 hover:bg-opacity-10'
									style={{
										borderColor: colors.brand.blue,
										color: colors.brand.blue,
										background: 'transparent',
									}}>
									SIGN IN
								</button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default ProfilePageComponent;
