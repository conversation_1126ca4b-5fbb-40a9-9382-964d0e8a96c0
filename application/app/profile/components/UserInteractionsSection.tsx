/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';
import { FaEdit, FaHeart, FaMapMarkerAlt, FaStar } from 'react-icons/fa';
import {
	UserFavoritesTab,
	UserLikesTab,
	UserReviewsTab,
	UserVisitsTab,
} from './interactions';

interface UserInteractionsSectionProps {
	userId: string;
	integrated?: boolean;
}

type TabType = 'likes' | 'favorites' | 'visits' | 'reviews';

interface TabConfig {
	id: TabType;
	label: string;
	icon: React.ReactNode;
	color: string;
	hoverColor: string;
	activeColor: string;
}

const tabs: TabConfig[] = [
	{
		id: 'likes',
		label: 'Likes',
		icon: <FaHeart className='w-4 h-4' />,
		color: colors.utility.error,
		hoverColor: colors.utility.errorLight,
		activeColor: colors.utility.error,
	},
	{
		id: 'favorites',
		label: 'Favorites',
		icon: <FaStar className='w-4 h-4' />,
		color: colors.brand.blue,
		hoverColor: colors.supporting.lightBlue,
		activeColor: colors.brand.blue,
	},
	{
		id: 'visits',
		label: 'Visits',
		icon: <FaMapMarkerAlt className='w-4 h-4' />,
		color: colors.brand.green,
		hoverColor: colors.supporting.mintGreen,
		activeColor: colors.brand.green,
	},
	{
		id: 'reviews',
		label: 'Reviews',
		icon: <FaEdit className='w-4 h-4' />,
		color: colors.brand.navy,
		hoverColor: colors.supporting.softNavy,
		activeColor: colors.brand.navy,
	},
];

export const UserInteractionsSection: React.FC<
	UserInteractionsSectionProps
> = ({ userId, integrated = false }) => {
	const [activeTab, setActiveTab] = useState<TabType>('likes');

	const renderTabContent = () => {
		switch (activeTab) {
			case 'likes':
				return <UserLikesTab userId={userId} />;
			case 'favorites':
				return <UserFavoritesTab userId={userId} />;
			case 'visits':
				return <UserVisitsTab userId={userId} />;
			case 'reviews':
				return <UserReviewsTab userId={userId} />;
			default:
				return null;
		}
	};

	if (integrated) {
		// Integrated mode - clean design without duplicate title
		return (
			<div>
				{/* Tab Navigation - Square Edges */}
				<div className='grid grid-cols-2 lg:grid-cols-4 gap-2 md:gap-3 mb-4 md:mb-6'>
					{tabs.map((tab) => {
						const isActive = activeTab === tab.id;

						return (
							<button
								key={tab.id}
								onClick={() => setActiveTab(tab.id)}
								className={`flex items-center justify-center gap-2 px-3 py-2 md:px-4 md:py-3 border-2 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5 ${
									isActive ? 'shadow-lg' : 'hover:shadow-md'
								}`}
								style={{
									color: isActive ? 'white' : tab.color,
									borderColor: isActive ? tab.activeColor : colors.ui.gray200,
									backgroundColor: isActive
										? tab.activeColor
										: 'rgba(255, 255, 255, 0.9)',
								}}>
								<div
									className={`transition-transform duration-200 ${
										isActive ? 'scale-110' : ''
									}`}>
									{tab.icon}
								</div>
								<span className='font-medium text-xs md:text-sm'>
									{tab.label}
								</span>
							</button>
						);
					})}
				</div>

				{/* Content Area - Square Edges */}
				<div
					className='max-h-80 md:max-h-96 overflow-y-auto border p-3 md:p-4 scrollbar-hide'
					style={{
						borderColor: colors.ui.gray200,
						backgroundColor: 'rgba(249, 250, 251, 0.5)',
						scrollbarWidth: 'none' /* Firefox */,
						msOverflowStyle: 'none' /* IE and Edge */,
					}}>
					<style jsx>{`
						.scrollbar-hide::-webkit-scrollbar {
							display: none; /* Chrome, Safari, Opera */
						}
					`}</style>
					{renderTabContent()}
				</div>
			</div>
		);
	}

	// Standalone mode - following design rules
	return (
		<div
			className='border backdrop-blur-sm shadow-lg overflow-hidden'
			style={{
				background: 'rgba(255, 255, 255, 0.9)',
				borderColor: colors.ui.gray200,
			}}>
			{/* Header - Square Edges */}
			<div
				className='px-4 py-3 md:px-6 md:py-4 border-b'
				style={{
					backgroundColor: colors.ui.blue50,
					borderColor: colors.ui.gray200,
				}}>
				<h2
					className='text-lg md:text-xl lg:text-2xl font-semibold'
					style={{ color: colors.neutral.textBlack }}>
					My Interactions
				</h2>
				<p
					className='text-xs md:text-sm mt-1'
					style={{ color: colors.neutral.slateGray }}>
					Manage your likes, favorites, visits, and reviews
				</p>
			</div>

			{/* Tab Navigation - Square Edges */}
			<div
				className='flex border-b'
				style={{ borderColor: colors.ui.gray200 }}>
				{tabs.map((tab) => {
					const isActive = activeTab === tab.id;

					return (
						<button
							key={tab.id}
							onClick={() => setActiveTab(tab.id)}
							className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 md:px-4 md:py-3 text-xs md:text-sm font-medium transition-all duration-200 ${
								isActive ? 'border-b-2' : ''
							}`}
							style={{
								color: isActive ? tab.activeColor : colors.neutral.slateGray,
								borderBottomColor: isActive ? tab.activeColor : 'transparent',
								backgroundColor: isActive ? `${tab.color}10` : 'transparent',
							}}>
							<div style={{ color: isActive ? tab.activeColor : tab.color }}>
								{tab.icon}
							</div>
							<span>{tab.label}</span>
						</button>
					);
				})}
			</div>

			{/* Clean Tab Content */}
			<div className='p-6'>{renderTabContent()}</div>
		</div>
	);
};
