/** @format */

'use client';

import { LoadingSpinner } from '@/app/shared/system';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { ProfilePageComponent } from './components';

export default function ProfilePageModule() {
	const { status } = useSession();
	const router = useRouter();

	useEffect(() => {
		if (status === 'unauthenticated') {
			router.push('/');
		}
	}, [status, router]);

	if (status === 'loading') {
		return <LoadingSpinner text='Loading profile...' />;
	}

	if (status === 'unauthenticated') {
		return null; // Will redirect to home
	}

	return <ProfilePageComponent />;
}
