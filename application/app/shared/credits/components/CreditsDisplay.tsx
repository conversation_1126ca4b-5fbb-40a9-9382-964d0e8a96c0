/** @format */

import { colors } from '@/app/colors';
import { useCreditsData } from '@/app/shared/credits/hooks/useCreditsData';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import React from 'react';
import { FiInfo, FiLoader, FiPlus, FiZap } from 'react-icons/fi';

interface CreditsDisplayProps {
	credits?: number; // Made optional since we'll load from database
	creditsType?: 'subscription' | 'purchased' | 'earned'; // Made optional
	showAddButton?: boolean;
	size?: 'small' | 'medium' | 'large';
	className?: string;
}

const CreditsDisplay: React.FC<CreditsDisplayProps> = ({
	credits: propCredits,
	creditsType: propCreditsType,
	showAddButton = true,
	size = 'medium',
	className = '',
}) => {
	const router = useRouter();
	const { data: session } = useSession();

	// Don't show credits if user is not signed in
	if (!session) {
		return null;
	}

	// Load credits from database
	const { availableCredits, subscriptionStatus, loading, error } =
		useCreditsData({
			userId: session?.user?.id,
			autoLoad: true,
		});

	// Use prop values if provided, otherwise use database values
	const credits = propCredits !== undefined ? propCredits : availableCredits;
	const creditsType =
		propCreditsType ||
		(subscriptionStatus.active ? 'subscription' : 'purchased');

	const sizeClasses = {
		small: {
			container: 'px-3 py-2 h-10', // Rectangular sizing for navbar
			icon: 'w-4 h-4', // Increased to match other navbar buttons
			text: 'text-sm', // Increased to match other navbar buttons
			number: 'text-sm', // Consistent with text size
		},
		medium: {
			container: 'px-3 py-2',
			icon: 'w-4 h-4',
			text: 'text-sm',
			number: 'text-lg',
		},
		large: {
			container: 'px-4 py-3',
			icon: 'w-5 h-5',
			text: 'text-base',
			number: 'text-xl',
		},
	};

	const currentSize = sizeClasses[size];

	const getCreditsDisplay = () => {
		if (loading) {
			return '...';
		}
		if (error) {
			return '?';
		}
		if (creditsType === 'subscription') {
			return '∞';
		}
		return credits.toString();
	};

	const getCreditsLabel = () => {
		if (loading) {
			return 'Loading';
		}
		if (error) {
			return 'Error';
		}
		if (creditsType === 'subscription') {
			return 'Unlimited';
		}
		return 'Credits';
	};

	const getBackgroundColor = () => {
		switch (creditsType) {
			case 'subscription':
				return colors.ui.green100;
			case 'purchased':
				return colors.ui.blue100;
			case 'earned':
				return colors.ui.green100;
			default:
				return colors.ui.gray100;
		}
	};

	const getTextColor = () => {
		switch (creditsType) {
			case 'subscription':
				return colors.brand.green;
			case 'purchased':
				return colors.brand.blue;
			case 'earned':
				return colors.brand.green;
			default:
				return colors.neutral.slateGray;
		}
	};

	return (
		<div className={`flex items-center gap-1.5 ${className}`}>
			{/* Credits Display */}
			<div
				className={`flex items-center justify-center border ${
					currentSize.container
				} ${
					size === 'small'
						? 'cursor-pointer hover:opacity-80 transition-opacity'
						: 'gap-1.5'
				}`}
				style={{
					backgroundColor: getBackgroundColor(),
					borderColor: size === 'small' ? colors.ui.gray200 : 'transparent',
					boxShadow:
						size === 'small' ? '0 2px 8px rgba(0, 0, 0, 0.05)' : 'none',
				}}
				onClick={size === 'small' ? () => router.push('/credits') : undefined}
				title={size === 'small' ? 'View Credits' : undefined}>
				{loading ? (
					<FiLoader
						className={`${currentSize.icon} animate-spin`}
						style={{ color: getTextColor() }}
					/>
				) : (
					<FiZap
						className={currentSize.icon}
						style={{ color: getTextColor() }}
					/>
				)}
				{size === 'small' ? (
					<div
						className={`font-bold ${currentSize.number} leading-none ml-1`}
						style={{ color: getTextColor() }}>
						{loading ? '...' : getCreditsDisplay()}
					</div>
				) : (
					<div className='flex items-center gap-1'>
						<div
							className={`font-bold ${currentSize.number} leading-none`}
							style={{ color: getTextColor() }}>
							{getCreditsDisplay()}
						</div>
						<div
							className={`${currentSize.text} leading-none whitespace-nowrap`}
							style={{ color: getTextColor() }}>
							{getCreditsLabel()}
						</div>
					</div>
				)}

				{/* Info Button - only for medium and large sizes */}
				{size !== 'small' && (
					<button
						onClick={() => router.push('/credits')}
						className='p-1 rounded transition-colors'
						style={{ color: getTextColor() }}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = 'transparent';
						}}
						title='Learn about credits'>
						<FiInfo className={currentSize.icon} />
					</button>
				)}
			</div>

			{/* Add Credits Button - only for medium and large sizes */}
			{showAddButton && creditsType !== 'subscription' && size !== 'small' && (
				<button
					onClick={() => router.push('/credits')}
					className={`flex items-center gap-1.5 border transition-all duration-200 ${currentSize.container}`}
					style={{
						backgroundColor: colors.brand.blue,
						color: 'white',
						borderColor: colors.brand.blue,
						boxShadow: `0 2px 8px ${colors.brand.blue}40`,
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.transform = 'translateY(-1px)';
						e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.transform = 'translateY(0)';
						e.currentTarget.style.boxShadow = 'none';
					}}>
					<FiPlus className={currentSize.icon} />
					<span className={`font-medium ${currentSize.text} whitespace-nowrap`}>
						Add
					</span>
				</button>
			)}
		</div>
	);
};

export default CreditsDisplay;
