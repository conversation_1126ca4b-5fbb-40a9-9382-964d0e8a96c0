/** @format */

'use client';

import { colors, gradients } from '@/app/colors';
import { CreditsDisplay } from '@/app/shared/credits';
import { useLocationManager } from '@/app/shared/locationManager';
import { useSessionManager } from '@/app/shared/system';
import { Z_INDEX_LAYERS } from '@/lib/navigation-config';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import {
	FaBars,
	FaCog,
	FaComments,
	FaGlobe,
	FaMap,
	FaMapMarkerAlt,
	FaSignOutAlt,
	FaStar,
	FaUser,
} from 'react-icons/fa';
import { FiRefreshCw, FiZap } from 'react-icons/fi';

interface AppNavBarProps {
	variant?: 'default' | 'transparent' | 'minimal';
}

const AppNavBar: React.FC<AppNavBarProps> = ({ variant = 'default' }) => {
	const router = useRouter();
	const pathname = usePathname();
	const { data: session } = useSession();
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const mobileMenuRef = useRef<HTMLDivElement>(null);
	const {
		location: userLocation,
		error: locationError,
		isLoading: locationLoading,
		requestAutoLocation,
	} = useLocationManager();

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setDropdownOpen(false);
			}
			if (
				mobileMenuRef.current &&
				!mobileMenuRef.current.contains(event.target as Node)
			) {
				setMobileMenuOpen(false);
			}
		};
		if (dropdownOpen || mobileMenuOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			return () =>
				document.removeEventListener('mousedown', handleClickOutside);
		}
	}, [dropdownOpen, mobileMenuOpen]);

	const [isLoggingOut, setIsLoggingOut] = useState(false);
	const { clearSession } = useSessionManager();

	const handleLogout = async () => {
		try {
			setIsLoggingOut(true);
			setDropdownOpen(false);
			await clearSession();
		} catch (error) {
			console.error('Logout failed:', error);
			// Force refresh as fallback
			if (typeof window !== 'undefined') {
				window.location.href = '/';
			}
		} finally {
			setIsLoggingOut(false);
		}
	};

	const getUserInitials = () => {
		if (session?.user?.username) {
			return session.user.username.substring(0, 2).toUpperCase();
		}
		if (session?.user?.email) {
			return session.user.email.substring(0, 2).toUpperCase();
		}
		return 'U';
	};

	// Permission-aware role checks
	const user = session?.user;
	const isSuperuser = user?.role === 'superuser';
	const isAgent = user?.role === 'agent';
	const hasFullAccess = user?.permissions?.includes('full_access');
	const hasUserManagement = user?.permissions?.includes('user_management');

	// Show admin dashboard if superuser with full_access or user_management
	const showAdminDashboard =
		isSuperuser && (hasFullAccess || hasUserManagement);
	// Show agent dashboard if agent with full_access or superuser with full_access
	const showAgentDashboard =
		(isAgent && hasFullAccess) || (isSuperuser && hasFullAccess);

	// Navigation helpers
	const handleNavLink = (id: string) => {
		try {
			if (pathname === '/') {
				const section = document.getElementById(id);
				if (section) {
					section.scrollIntoView({ behavior: 'smooth' });
					return;
				}
			}
			router.push(`/#${id}`);
		} catch (error) {
			console.error('Navigation failed:', error);
		}
	};

	const handleRouteNavigation = (route: string) => {
		try {
			// Check if user needs authentication for protected routes
			if (
				(route === '/chat' || route === '/profile' || route === '/settings') &&
				!session
			) {
				router.push(`/auth?callbackUrl=${encodeURIComponent(route)}`);
				return;
			}
			router.push(route);
		} catch (error) {
			console.error('Route navigation failed:', error);
		}
	};

	const handleSignIn = () => {
		try {
			// Pass current page as callback URL so user returns to where they were
			const callbackUrl = encodeURIComponent(pathname);
			router.push(`/auth?mode=signin&callbackUrl=${callbackUrl}`);
		} catch (error) {
			console.error('Sign in navigation failed:', error);
		}
	};

	const formatLocation = () => {
		if (!userLocation) return 'Location unavailable';
		const latDir = userLocation.latitude >= 0 ? 'N' : 'S';
		const lngDir = userLocation.longitude >= 0 ? 'E' : 'W';
		const timeAgo = Math.floor((Date.now() - userLocation.timestamp) / 1000);
		const timeText =
			timeAgo < 60 ? 'just now' : `${Math.floor(timeAgo / 60)}m ago`;
		const accuracy = userLocation.accuracy
			? Math.round(userLocation.accuracy)
			: 'unknown';
		return `${Math.abs(userLocation.latitude).toFixed(4)}°${latDir}, ${Math.abs(
			userLocation.longitude
		).toFixed(4)}°${lngDir} (±${accuracy}m) • ${timeText}`;
	};

	// Get navigation styles based on variant
	const getNavigationStyles = () => {
		const baseClasses =
			'fixed top-0 left-0 right-0 flex items-center justify-between px-4 md:px-8 h-16 transition-all duration-300';
		const zIndex = Z_INDEX_LAYERS.navigation;

		switch (variant) {
			case 'transparent':
				return {
					className: `${baseClasses} ${zIndex} backdrop-blur-md border-b border-opacity-20`,
					style: {
						background: 'rgba(255, 255, 255, 0.1)',
						borderColor: 'rgba(255, 255, 255, 0.2)',
					},
				};
			case 'minimal':
				return {
					className: `${baseClasses} ${zIndex} bg-white/90 backdrop-blur-sm shadow-sm`,
					style: { borderBottom: `1px solid ${colors.ui.gray100}` },
				};
			default:
				return {
					className: `${baseClasses} ${zIndex} border-b shadow-sm animate-fade-in bg-white`,
					style: {
						background: gradients.background,
						borderColor: colors.ui.gray200,
					},
				};
		}
	};

	const navStyles = getNavigationStyles();

	return (
		<nav
			className={navStyles.className}
			style={navStyles.style}>
			{/* Logo Only */}
			<div
				className='flex items-center cursor-pointer h-full flex-shrink-0'
				onClick={() => router.push('/')}>
				<div className='flex items-center justify-center w-12 h-12'>
					<Image
						src='/logo/512x512.png'
						alt='Wizlop Logo'
						width={48}
						height={48}
						className='object-contain'
						priority
					/>
				</div>
			</div>
			{/* Main Nav Links - Responsive */}
			<div className='flex-1 flex justify-center'>
				<div
					className='hidden lg:flex items-center space-x-6'
					style={{ color: colors.neutral.slateGray }}>
					<button
						onClick={() => handleRouteNavigation('/chat')}
						className='flex items-center space-x-2 px-3 py-2 transition-colors text-sm border'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
							borderColor: colors.ui.blue200,
							boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.blue200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.blue200}40`;
						}}>
						<FaComments className='w-4 h-4' />
						<span>Chat</span>
					</button>
					<button
						onClick={() => handleRouteNavigation('/globe')}
						className='flex items-center space-x-2 px-3 py-2 transition-colors text-sm border'
						style={{
							backgroundColor: colors.ui.green100,
							color: colors.brand.green,
							borderColor: colors.ui.green200,
							boxShadow: `0 2px 8px ${colors.ui.green200}40`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.green200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.green200}40`;
						}}>
						<FaGlobe className='w-4 h-4' />
						<span>Globe</span>
					</button>
					<button
						onClick={() => handleRouteNavigation('/pois')}
						className='flex items-center space-x-2 px-3 py-2 transition-colors text-sm border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.supporting.teal,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaMapMarkerAlt className='w-4 h-4' />
						<span>POIs</span>
					</button>
				</div>

				{/* Compact nav for medium screens */}
				<div className='hidden md:flex lg:hidden items-center space-x-2'>
					<button
						onClick={() => handleRouteNavigation('/chat')}
						className='flex items-center justify-center w-10 h-10 transition-colors border'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
							borderColor: colors.ui.blue200,
							boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
						}}
						title='Chat'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.blue200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.blue200}40`;
						}}>
						<FaComments className='w-4 h-4' />
					</button>
					<button
						onClick={() => handleRouteNavigation('/globe')}
						className='flex items-center justify-center w-10 h-10 transition-colors border'
						style={{
							backgroundColor: colors.ui.green100,
							color: colors.brand.green,
							borderColor: colors.ui.green200,
							boxShadow: `0 2px 8px ${colors.ui.green200}40`,
						}}
						title='Globe'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.green200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.green200}40`;
						}}>
						<FaGlobe className='w-4 h-4' />
					</button>
					<button
						onClick={() => handleRouteNavigation('/pois')}
						className='flex items-center justify-center w-10 h-10 transition-colors border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.supporting.teal,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						title='POIs'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaMapMarkerAlt className='w-4 h-4' />
					</button>
				</div>
			</div>

			{/* Right Side - Credits and Profile Dropdown */}
			<div className='flex items-center space-x-2 flex-shrink-0'>
				{/* Add POI button, only on /pois - hidden on small screens */}
				{pathname === '/pois' && (
					<button
						onClick={() => router.push('/pois/submit')}
						className='hidden sm:flex px-3 py-2 bg-green-600 text-white font-semibold shadow-md hover:bg-green-700 transition-colors text-sm border border-green-700'
						style={{
							minWidth: 100,
							boxShadow: '0 2px 8px rgba(34, 197, 94, 0.3)',
						}}>
						+ Add POI
					</button>
				)}

				{session ? (
					<div className='flex items-center space-x-2'>
						{/* Credits - hidden on small screens */}
						<div className='hidden md:block'>
							<CreditsDisplay
								size='medium'
								showAddButton={false}
							/>
						</div>

						{/* Profile Dropdown */}
						<div
							className='relative'
							ref={dropdownRef}>
							<div
								onClick={() => setDropdownOpen(!dropdownOpen)}
								className='w-10 h-10 text-white flex items-center justify-center cursor-pointer select-none transition-all duration-300 shadow-lg border border-white/20'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
									boxShadow: `0 4px 12px ${colors.brand.blue}40`,
								}}
								onMouseEnter={(e) => {
									e.currentTarget.style.background = `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.brand.green} 100%)`;
									e.currentTarget.style.boxShadow = `0 6px 16px ${colors.brand.green}40`;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`;
									e.currentTarget.style.boxShadow = `0 4px 12px ${colors.brand.blue}40`;
								}}>
								{getUserInitials()}
							</div>
							{dropdownOpen && (
								<div
									className='absolute right-0 mt-2 w-80 bg-white border border-gray-200 shadow-xl z-50 py-2 text-sm overflow-hidden'
									style={{
										boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
										borderColor: colors.ui.gray200,
									}}>
									{/* Location Display */}
									<div className='px-4 py-3 bg-gray-50 border-b border-gray-100'>
										<div className='flex items-start gap-3'>
											<FaMapMarkerAlt className='text-blue-600 mt-0.5 flex-shrink-0' />
											<div className='min-w-0 flex-1'>
												<div className='flex items-center justify-between mb-1'>
													<div className='flex items-center gap-2'>
														<span className='font-medium text-gray-800'>
															Your Location
														</span>
														{userLocation && (
															<div
																className='w-2 h-2 bg-green-500 animate-pulse'
																title='Location active'></div>
														)}
													</div>
													<button
														onClick={requestAutoLocation}
														disabled={locationLoading}
														className='text-blue-600 hover:text-blue-800 disabled:opacity-50 transition-colors'
														title='Refresh location'>
														<FiRefreshCw
															className={`w-3 h-3 ${
																locationLoading ? 'animate-spin' : ''
															}`}
														/>
													</button>
												</div>
												<div className='text-xs text-gray-600 break-all'>
													{locationLoading ? (
														<span className='text-blue-500'>
															📍 Getting location...
														</span>
													) : locationError ? (
														<span className='text-red-600'>
															⚠️ {locationError}
														</span>
													) : (
														formatLocation()
													)}
												</div>
											</div>
										</div>
									</div>
									<button
										onClick={() => {
											setDropdownOpen(false);
											router.push('/profile');
										}}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-blue-50 transition-colors text-blue-700 hover:text-blue-900'>
										<FaUser className='text-base' /> Profile
									</button>
									<button
										onClick={() => {
											setDropdownOpen(false);
											router.push('/credits');
										}}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-yellow-50 transition-colors text-yellow-700 hover:text-yellow-900'>
										<FiZap className='text-base' /> Credits
									</button>
									<button
										onClick={() => {
											setDropdownOpen(false);
											router.push('/settings');
										}}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors text-gray-700 hover:text-gray-900'>
										<FaCog className='text-base' /> Settings
									</button>
									{showAdminDashboard && (
										<button
											onClick={() => {
												setDropdownOpen(false);
												router.push('/admin/dashboard');
											}}
											className='w-full flex items-center gap-3 px-4 py-3 hover:bg-green-50 transition-colors text-green-700 hover:text-green-900'>
											<FaStar className='text-base' /> Admin Dashboard
										</button>
									)}
									{showAgentDashboard && (
										<button
											onClick={() => {
												setDropdownOpen(false);
												router.push('/agent/dashboard');
											}}
											className='w-full flex items-center gap-3 px-4 py-3 hover:bg-green-50 transition-colors text-green-700 hover:text-green-900'>
											<FaMap className='text-base' /> Agent Dashboard
										</button>
									)}
									<div className='border-t border-gray-200 my-1' />
									<button
										onClick={handleLogout}
										disabled={isLoggingOut}
										className='w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-colors text-red-600 hover:text-red-700 disabled:opacity-50'>
										<FaSignOutAlt className='text-base' />
										{isLoggingOut ? 'Logging out...' : 'Logout'}
									</button>
								</div>
							)}
						</div>
					</div>
				) : (
					<button
						onClick={handleSignIn}
						className='hidden sm:flex px-4 py-2 bg-blue-600 text-white font-semibold shadow-md hover:bg-blue-700 transition-colors text-sm border border-blue-700'
						style={{
							minWidth: 100,
							boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
						}}>
						Sign In
					</button>
				)}

				{/* Mobile Menu Button */}
				<button
					onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
					className='md:hidden p-2 transition-colors border'
					style={{
						backgroundColor: mobileMenuOpen ? colors.ui.gray200 : 'transparent',
						color: colors.neutral.slateGray,
						borderColor: mobileMenuOpen ? colors.ui.gray300 : colors.ui.gray200,
						boxShadow: mobileMenuOpen
							? `0 2px 8px ${colors.ui.gray200}60`
							: 'none',
					}}
					aria-label='Toggle mobile menu'>
					<FaBars className='w-5 h-5' />
				</button>
			</div>

			{/* Mobile Menu Dropdown */}
			{mobileMenuOpen && (
				<div
					ref={mobileMenuRef}
					className={`absolute top-16 left-0 right-0 bg-white border-b shadow-lg md:hidden ${Z_INDEX_LAYERS.dropdown}`}
					style={{
						background: gradients.background,
						borderColor: colors.ui.gray200,
					}}>
					<div className='px-4 py-4 space-y-3 max-h-[80vh] overflow-y-auto'>
						{/* Credits display for mobile */}
						{session && (
							<div
								className='mb-4 p-3 border'
								style={{
									backgroundColor: colors.ui.blue50,
									borderColor: colors.ui.blue200,
									boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
								}}>
								<CreditsDisplay
									size='medium'
									showAddButton={false}
								/>
							</div>
						)}

						{/* Add POI button, only on /pois */}
						{pathname === '/pois' && (
							<button
								onClick={() => {
									handleRouteNavigation('/pois/submit');
									setMobileMenuOpen(false);
								}}
								className='w-full py-3 px-3 bg-green-600 text-white font-semibold transition-colors mb-2 border border-green-700'
								style={{
									boxShadow: '0 2px 8px rgba(34, 197, 94, 0.3)',
								}}>
								+ Add POI
							</button>
						)}

						{/* Main navigation buttons */}
						<button
							onClick={() => {
								handleRouteNavigation('/chat');
								setMobileMenuOpen(false);
							}}
							className='w-full flex items-center space-x-3 py-3 px-3 transition-colors border'
							style={{
								backgroundColor: colors.ui.blue100,
								color: colors.brand.blue,
								borderColor: colors.ui.blue200,
								boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
							}}>
							<FaComments className='w-4 h-4' />
							<span>Chat</span>
						</button>

						<button
							onClick={() => {
								handleRouteNavigation('/globe');
								setMobileMenuOpen(false);
							}}
							className='w-full flex items-center space-x-3 py-3 px-3 transition-colors border'
							style={{
								backgroundColor: colors.ui.green100,
								color: colors.brand.green,
								borderColor: colors.ui.green200,
								boxShadow: `0 2px 8px ${colors.ui.green200}40`,
							}}>
							<FaGlobe className='w-4 h-4' />
							<span>Globe</span>
						</button>

						<button
							onClick={() => {
								handleRouteNavigation('/pois');
								setMobileMenuOpen(false);
							}}
							className='w-full flex items-center space-x-3 py-3 px-3 transition-colors border'
							style={{
								backgroundColor: colors.ui.gray100,
								color: colors.supporting.teal,
								borderColor: colors.ui.gray200,
								boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
							}}>
							<FaMapMarkerAlt className='w-4 h-4' />
							<span>POIs</span>
						</button>

						{/* User section */}
						{session ? (
							<>
								<div className='border-t border-gray-200 my-2' />
								<div className='py-2'>
									<CreditsDisplay
										size='small'
										showAddButton={false}
									/>
								</div>
								<button
									onClick={() => {
										handleRouteNavigation('/profile');
										setMobileMenuOpen(false);
									}}
									className='w-full flex items-center space-x-3 py-3 px-3 transition-colors border'
									style={{
										color: colors.neutral.slateGray,
										borderColor: colors.ui.gray200,
										backgroundColor: 'transparent',
									}}>
									<FaUser className='w-4 h-4' />
									<span>Profile</span>
								</button>
								<button
									onClick={() => {
										handleRouteNavigation('/settings');
										setMobileMenuOpen(false);
									}}
									className='w-full flex items-center space-x-3 py-3 px-3 transition-colors border'
									style={{
										color: colors.neutral.slateGray,
										borderColor: colors.ui.gray200,
										backgroundColor: 'transparent',
									}}>
									<FaCog className='w-4 h-4' />
									<span>Settings</span>
								</button>
								<button
									onClick={() => {
										handleLogout();
										setMobileMenuOpen(false);
									}}
									disabled={isLoggingOut}
									className='w-full flex items-center space-x-3 py-3 px-3 transition-colors text-red-600 disabled:opacity-50 border border-red-200'>
									<FaSignOutAlt className='w-4 h-4' />
									<span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
								</button>
							</>
						) : (
							<>
								<div className='border-t border-gray-200 my-2' />
								<button
									onClick={() => {
										handleSignIn();
										setMobileMenuOpen(false);
									}}
									className='w-full py-3 px-3 bg-blue-600 text-white font-semibold transition-colors border border-blue-700'
									style={{
										boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
									}}>
									Sign In
								</button>
							</>
						)}
					</div>
				</div>
			)}
		</nav>
	);
};

export default AppNavBar;
