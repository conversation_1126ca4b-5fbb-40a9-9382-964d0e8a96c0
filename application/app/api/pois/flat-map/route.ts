/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/flat-map - Get POIs for flat map visualization using top_location algorithm
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);

		// Parse bounds parameter
		const bounds = searchParams.get('bounds');
		const category = searchParams.get('category');
		const subcategory = searchParams.get('subcategory');
		const city = searchParams.get('city');
		const district = searchParams.get('district');
		const neighborhood = searchParams.get('neighborhood');
		const country = searchParams.get('country');
		const limit = searchParams.get('limit')
			? parseInt(searchParams.get('limit')!)
			: 100;
		const offset = searchParams.get('offset')
			? parseInt(searchParams.get('offset')!)
			: 0;

		// Parse bounds if provided
		let minLat, maxLat, minLng, maxLng;
		if (bounds) {
			const boundsArray = bounds.split(',').map(Number);
			if (boundsArray.length === 4) {
				[minLat, minLng, maxLat, maxLng] = boundsArray;
			} else {
				return NextResponse.json(
					{
						error:
							'Invalid bounds format. Expected: minLat,minLng,maxLat,maxLng',
					},
					{ status: 400 }
				);
			}
		} else {
			return NextResponse.json(
				{ error: 'Bounds parameter is required' },
				{ status: 400 }
			);
		}

		// Get user session for personalized results
		const session = await getServerSession(authOptions);
		const userId = session?.user?.id;

		logger.info('Fetching POIs for flat map using top_location algorithm', {
			bounds: { minLat, maxLat, minLng, maxLng },
			category,
			subcategory,
			city,
			district,
			neighborhood,
			country,
			limit,
			offset,
			userId,
		});

		// Use the top_location algorithm with spatial bounds
		const result = await db.query(
			`SELECT * FROM spatial_schema.get_top_locations(
				$1, $2, $3, $4, $5, $6, NULL, NULL, NULL, NULL, $7, $8, $9, $10, $11, $12
			)`,
			[
				category || null,
				subcategory || null,
				city || null,
				district || null,
				neighborhood || null,
				country || null,
				minLat,
				maxLat,
				minLng,
				maxLng,
				offset,
				limit,
			]
		);

		// Transform results to match expected format for flat-map
		const pois = result.rows.map((row) => ({
			id: row.id,
			poi_type: 'official', // All POIs from top_location are official
			poi_id: row.id,
			temp_id: null,
			approved_id: null,
			name: row.name,
			category: row.category,
			subcategory: row.subcategory,
			cuisine: row.cuisine,
			city: row.city,
			district: row.district,
			neighborhood: row.neighborhood,
			country: row.country,
			latitude: parseFloat(row.latitude),
			longitude: parseFloat(row.longitude),
			phone_number: row.phone_number,
			opening_hours: row.opening_hours,
			description: row.description,
			user_rating_avg: row.user_rating_avg
				? parseFloat(row.user_rating_avg)
				: null,
			user_rating_count: row.user_rating_count || 0,
			view_count: row.view_count || 0,
			favorite_count: row.favorite_count || 0,
			visit_count: row.visit_count || 0,
			like_count: row.like_count || 0,
			review_count: row.review_count || 0,
			popularity_score: row.popularity_score || 0,
			trending_score: row.trending_score || 0,
			final_score: row.final_score || 0,
			score_breakdown: row.score_breakdown || {},
			is_favorite: false, // TODO: Add user favorites support
		}));

		logger.info(
			'Successfully fetched flat map POIs using top_location algorithm',
			{
				count: pois.length,
				bounds: { minLat, maxLat, minLng, maxLng },
				filters: {
					category,
					subcategory,
					city,
					district,
					neighborhood,
					country,
				},
			}
		);

		return NextResponse.json({
			success: true,
			pois,
			total: pois.length,
			bounds: { minLat, maxLat, minLng, maxLng },
			filters: {
				category,
				subcategory,
				city,
				district,
				neighborhood,
				country,
			},
		});
	} catch (error) {
		logger.error(
			'Error fetching flat map POIs:',
			error as Record<string, unknown>
		);
		return NextResponse.json(
			{ error: 'Failed to fetch POIs for flat map' },
			{ status: 500 }
		);
	}
}

// POST /api/pois/flat-map - Create a new POI from flat map
export async function POST(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const {
			name,
			category,
			subcategory,
			latitude,
			longitude,
			description,
			phone_number,
			opening_hours,
		} = body;

		// Validate required fields
		if (!name || !category || !latitude || !longitude) {
			return NextResponse.json(
				{ error: 'Name, category, latitude, and longitude are required' },
				{ status: 400 }
			);
		}

		logger.info('Creating new POI from flat map', {
			name,
			category,
			subcategory,
			latitude,
			longitude,
			userId: session.user.id,
		});

		// Insert new POI
		const sql = `
			INSERT INTO spatial_schema.temp_pois (
				name, category, subcategory, latitude, longitude,
				description, phone_number, opening_hours, user_id, created_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
			RETURNING id, name, category, subcategory, latitude, longitude
		`;

		const params = [
			name,
			category,
			subcategory || null,
			latitude,
			longitude,
			description || null,
			phone_number || null,
			opening_hours || null,
			session.user.id,
		];

		const result = await db.query(sql, params);
		const newPOI = result.rows[0];

		logger.info('Successfully created POI from flat map', {
			poiId: newPOI.id,
			name: newPOI.name,
		});

		return NextResponse.json({
			success: true,
			poi: {
				id: newPOI.id,
				poi_type: 'temp',
				temp_id: newPOI.id,
				name: newPOI.name,
				category: newPOI.category,
				subcategory: newPOI.subcategory,
				latitude: parseFloat(newPOI.latitude),
				longitude: parseFloat(newPOI.longitude),
			},
		});
	} catch (error) {
		logger.error(
			'Error creating POI from flat map:',
			error as Record<string, unknown>
		);
		return NextResponse.json(
			{ error: 'Failed to create POI' },
			{ status: 500 }
		);
	}
}
