/** @format */

import { db, table } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { withSecurityHeaders } from '@/lib/security-headers';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

interface AuthenticatedRequest extends NextRequest {
	user: {
		id: string;
		role: string;
	};
}

// GET /api/user/credits/transactions - Get user's credit transaction history
async function getTransactionsHandler(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const { searchParams } = new URL(request.url);
		const userId = searchParams.get('userId') || session.user.id;
		const limit = parseInt(searchParams.get('limit') || '20');
		const offset = parseInt(searchParams.get('offset') || '0');
		const transactionType = searchParams.get('type'); // 'earn', 'purchase', 'use', 'refund'

		// Only allow users to see their own transactions unless they're an agent/superuser
		if (
			userId !== session.user.id &&
			!['agent', 'superuser'].includes(session.user.role)
		) {
			return NextResponse.json(
				{ success: false, error: 'Access denied' },
				{ status: 403 }
			);
		}

		// Build query
		let query = `SELECT * FROM ${table(
			'credit_transactions'
		)} WHERE user_id = $1`;
		const params: unknown[] = [userId];

		if (transactionType) {
			query += ` AND transaction_type = $2`;
			params.push(transactionType);
		}

		query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${
			params.length + 2
		}`;
		params.push(limit, offset);

		// Get transactions with pagination
		const rawTransactions = await db.getMany(query, params);

		// Import creditsToDecimal function for converting database integers to display values
		const { creditsToDecimal } = await import('@/lib/utils/credits');

		// Convert transaction amounts from database integers to decimal for display
		const transactions = rawTransactions.map((transaction) => ({
			...transaction,
			amount: creditsToDecimal(transaction.amount),
		}));

		// Get total count for pagination
		let countQuery = `SELECT COUNT(*) as total FROM ${table(
			'credit_transactions'
		)} WHERE user_id = $1`;
		const countParams: unknown[] = [userId];

		if (transactionType) {
			countQuery += ` AND transaction_type = $2`;
			countParams.push(transactionType);
		}

		const countResult = await db.getOne(countQuery, countParams);
		const totalCount = parseInt(countResult?.total || '0');

		return NextResponse.json({
			success: true,
			transactions,
			pagination: {
				total: totalCount,
				limit,
				offset,
				hasMore: offset + limit < totalCount,
			},
		});
	} catch (error) {
		logger.error('Error fetching credit transactions', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch transactions' },
			{ status: 500 }
		);
	}
}

// POST /api/user/credits/transactions - Record a credit transaction (internal use)
async function postTransactionHandler(request: AuthenticatedRequest) {
	try {
		const body = await request.json();
		const {
			userId,
			transactionType,
			amount,
			reason,
			description,
			relatedEntityType,
			relatedEntityId,
			metadata = {},
		} = body;

		// Validate required fields
		if (!userId || !transactionType || !amount || !reason) {
			return NextResponse.json(
				{
					success: false,
					error: 'userId, transactionType, amount, and reason are required',
				},
				{ status: 400 }
			);
		}

		if (!['earn', 'purchase', 'use', 'refund'].includes(transactionType)) {
			return NextResponse.json(
				{
					success: false,
					error:
						'Invalid transaction type. Must be earn, purchase, use, or refund',
				},
				{ status: 400 }
			);
		}

		if (amount <= 0) {
			return NextResponse.json(
				{ success: false, error: 'Amount must be positive' },
				{ status: 400 }
			);
		}

		// Only allow users to create transactions for themselves unless they're an agent/superuser
		if (
			userId !== request.user.id &&
			!['agent', 'superuser'].includes(request.user.role)
		) {
			return NextResponse.json(
				{ success: false, error: 'Access denied' },
				{ status: 403 }
			);
		}

		// Record the transaction
		const transaction = await db.insert(table('credit_transactions'), {
			user_id: userId,
			transaction_type: transactionType,
			amount,
			reason,
			description,
			metadata,
			related_entity_type: relatedEntityType,
			related_entity_id: relatedEntityId,
		});

		logger.info('Credit transaction recorded', {
			transactionId: transaction.id,
			userId,
			transactionType,
			amount,
			reason,
		});

		return NextResponse.json({
			success: true,
			transaction,
		});
	} catch (error) {
		logger.error('Error recording credit transaction', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to record transaction' },
			{ status: 500 }
		);
	}
}

// Middleware to add user info to request
function withAuth(
	handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
	return async function (request: NextRequest): Promise<NextResponse> {
		const session = await getServerSession(authOptions);

		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		// Add user info to request
		const authenticatedRequest = request as AuthenticatedRequest;
		authenticatedRequest.user = {
			id: session.user.id,
			role: session.user.role || 'user',
		};

		return handler(authenticatedRequest);
	};
}

// Export with security headers and authentication
export const GET = withSecurityHeaders(getTransactionsHandler);
export const POST = withSecurityHeaders(withAuth(postTransactionHandler));
