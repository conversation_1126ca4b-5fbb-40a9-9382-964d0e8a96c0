/** @format */

import { AuthenticatedRequest, withAuth } from '@/lib/auth-middleware';
import { db, table } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { withSecurityHeaders } from '@/lib/security-headers';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/user/credits - Get user's credit information
async function getCreditsHandler() {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const userId = session.user.id;

		logger.info('Fetching user credits', { userId });

		const credits = await db.getOne(
			`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
			[userId]
		);

		// If no credits record exists, create one with defaults
		if (!credits) {
			const newCredits = await db.insert(table('user_credits'), {
				user_id: userId,
				credits_earned: 0,
				credits_purchased: 0,
				credits_used: 0,
				subscription_type: 'none',
			});

			return NextResponse.json({
				success: true,
				credits: newCredits,
				total_credits: 0,
				available_credits: 0,
			});
		}

		// Import creditsToDecimal function for converting database integers to display values
		const { creditsToDecimal } = await import('@/lib/utils/credits');

		// Calculate totals in database integer format
		const totalCreditsInteger =
			credits.credits_earned + credits.credits_purchased;
		const availableCreditsInteger = totalCreditsInteger - credits.credits_used;

		// Convert to decimal for display
		const totalCredits = creditsToDecimal(totalCreditsInteger);
		const availableCredits = creditsToDecimal(availableCreditsInteger);

		return NextResponse.json({
			success: true,
			credits,
			total_credits: totalCredits,
			available_credits: availableCredits,
		});
	} catch (error) {
		logger.error('Error fetching user credits', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch credits' },
			{ status: 500 }
		);
	}
}

// POST /api/user/credits - Update user credits (earn, purchase, use)
async function postCreditsHandler(request: AuthenticatedRequest) {
	try {
		const body = await request.json();
		const { action, amount, reason, subscriptionType, subscriptionExpiresAt } =
			body;

		if (!action || !amount) {
			return NextResponse.json(
				{ success: false, error: 'Action and amount are required' },
				{ status: 400 }
			);
		}

		const userId = request.user.id;

		if (!['earn', 'purchase', 'use'].includes(action)) {
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid action. Must be earn, purchase, or use',
				},
				{ status: 400 }
			);
		}

		if (amount <= 0) {
			return NextResponse.json(
				{ success: false, error: 'Amount must be positive' },
				{ status: 400 }
			);
		}

		logger.info('Processing credit transaction', {
			userId,
			action,
			amount,
			reason,
		});

		// Get current credits
		let credits = await db.getOne(
			`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
			[userId]
		);

		// Create credits record if it doesn't exist
		if (!credits) {
			credits = await db.insert(table('user_credits'), {
				user_id: userId,
				credits_earned: 0,
				credits_purchased: 0,
				credits_used: 0,
				subscription_type: 'none',
			});
		}

		// Calculate new values based on action
		const updateData: Record<string, number | string> = {};

		switch (action) {
			case 'earn':
				updateData.credits_earned = credits.credits_earned + amount;
				break;
			case 'purchase':
				updateData.credits_purchased = credits.credits_purchased + amount;
				if (subscriptionType) {
					updateData.subscription_type = subscriptionType;
				}
				if (subscriptionExpiresAt) {
					updateData.subscription_expires_at = subscriptionExpiresAt;
				}
				break;
			case 'use':
				const totalAvailable =
					credits.credits_earned +
					credits.credits_purchased -
					credits.credits_used;
				if (amount > totalAvailable) {
					return NextResponse.json(
						{ success: false, error: 'Insufficient credits' },
						{ status: 400 }
					);
				}
				updateData.credits_used = credits.credits_used + amount;
				break;
		}

		// Update credits
		const updatedCredits = await db.update(
			table('user_credits'),
			credits.id,
			updateData
		);

		const totalCredits =
			updatedCredits.credits_earned + updatedCredits.credits_purchased;
		const availableCredits = totalCredits - updatedCredits.credits_used;

		logger.info('Credit transaction completed', {
			userId,
			action,
			amount,
			newTotal: totalCredits,
			newAvailable: availableCredits,
		});

		return NextResponse.json({
			success: true,
			credits: updatedCredits,
			total_credits: totalCredits,
			available_credits: availableCredits,
			transaction: {
				action,
				amount,
				reason,
				timestamp: new Date().toISOString(),
			},
		});
	} catch (error) {
		logger.error('Error processing credit transaction', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to process credit transaction' },
			{ status: 500 }
		);
	}
}

// Export with security headers and authorization
export const GET = withSecurityHeaders(getCreditsHandler);
export const POST = withSecurityHeaders(withAuth(postCreditsHandler));

// PUT /api/user/credits - Update subscription information
export async function PUT(request: NextRequest) {
	try {
		// Get user session
		const session = await getServerSession(authOptions);
		if (!session?.user?.id) {
			return NextResponse.json(
				{ success: false, error: 'Authentication required' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const { subscriptionType, subscriptionExpiresAt } = body;

		if (!subscriptionType) {
			return NextResponse.json(
				{ success: false, error: 'Subscription type is required' },
				{ status: 400 }
			);
		}

		const userId = session.user.id;

		if (!['none', 'basic', 'premium'].includes(subscriptionType)) {
			return NextResponse.json(
				{ success: false, error: 'Invalid subscription type' },
				{ status: 400 }
			);
		}

		logger.info('Updating user subscription', { userId, subscriptionType });

		// Get current credits
		let credits = await db.getOne(
			`SELECT * FROM ${table('user_credits')} WHERE user_id = $1`,
			[userId]
		);

		// Create credits record if it doesn't exist
		if (!credits) {
			credits = await db.insert(table('user_credits'), {
				user_id: userId,
				credits_earned: 0,
				credits_purchased: 0,
				credits_used: 0,
				subscription_type: subscriptionType,
				subscription_expires_at: subscriptionExpiresAt,
			});
		} else {
			// Update subscription
			credits = await db.update(table('user_credits'), credits.id, {
				subscription_type: subscriptionType,
				subscription_expires_at: subscriptionExpiresAt,
			});
		}

		const totalCredits = credits.credits_earned + credits.credits_purchased;
		const availableCredits = totalCredits - credits.credits_used;

		return NextResponse.json({
			success: true,
			credits,
			total_credits: totalCredits,
			available_credits: availableCredits,
		});
	} catch (error) {
		logger.error('Error updating subscription', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to update subscription' },
			{ status: 500 }
		);
	}
}
