/** @format */

'use client';

import { colors } from '@/app/colors';
import Image from 'next/image';
import React from 'react';
import { FiHeart } from 'react-icons/fi';

const LandingFooter: React.FC = () => {
	return (
		<footer className='py-8 md:py-12 lg:py-16 relative bg-transparent'>
			{/* Profound Background Elements */}
			<div className='absolute inset-0 opacity-5'>
				<div
					className='absolute top-0 left-0 w-32 h-32 md:w-48 md:h-48'
					style={{
						background: `radial-gradient(circle, ${colors.brand.navy}40 0%, transparent 70%)`,
					}}
				/>
				<div
					className='absolute bottom-0 right-0 w-24 h-24 md:w-32 md:h-32 rotate-45'
					style={{
						background: `linear-gradient(45deg, ${colors.brand.blue}30 0%, transparent 70%)`,
					}}
				/>
			</div>

			<div className='relative w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
				{/* Asymmetric Grid Layout */}
				<div className='grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-8 lg:gap-12'>
					{/* Left Column - Enhanced Brand Section */}
					<div className='lg:col-span-5 space-y-4 md:space-y-6'>
						<div
							className='p-4 md:p-6 lg:p-8 border backdrop-blur-sm'
							style={{
								background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}90 0%, ${colors.ui.navy50}20 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 8px 32px rgba(1, 3, 79, 0.08)',
							}}>
							<div className='flex items-center space-x-3 md:space-x-4 mb-4 md:mb-6'>
								<div className='w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 relative flex-shrink-0'>
									<Image
										src='/logo/512x512.png'
										alt='Wizlop Logo'
										width={48}
										height={48}
										className=''
										style={{
											filter: `brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(2555%) hue-rotate(202deg) brightness(101%) contrast(101%)`,
										}}
									/>
								</div>
								<h3
									className='text-2xl sm:text-3xl font-bold'
									style={{ color: colors.brand.navy }}>
									Wizlop
								</h3>
							</div>
							<p
								className='mb-6 leading-relaxed text-base sm:text-lg'
								style={{ color: colors.neutral.slateGray }}>
								Revolutionizing location discovery through conversational AI.
								Find exactly what you're looking for in Istanbul.
							</p>
							<div
								className='flex items-center text-base'
								style={{ color: colors.neutral.slateGray }}>
								<span>Made with</span>
								<FiHeart
									className='mx-2 w-5 h-5'
									style={{ color: colors.brand.green }}
								/>
								<span>in Istanbul</span>
							</div>
						</div>
					</div>

					{/* Right Column - Enhanced Links & Info */}
					<div className='lg:col-span-7 grid grid-cols-1 sm:grid-cols-2 gap-8'>
						{/* Quick Links */}
						<div className='space-y-4'>
							<h4
								className='text-lg font-bold mb-4'
								style={{ color: colors.brand.navy }}>
								Quick Links
							</h4>
							<ul className='space-y-3'>
								<li>
									<button
										className='transition-all duration-300 text-left text-base hover:translate-x-2'
										style={{ color: colors.neutral.slateGray }}
										onMouseEnter={(e) => {
											e.currentTarget.style.color = colors.brand.blue;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.color = colors.neutral.slateGray;
										}}>
										Explore Categories
									</button>
								</li>
								<li>
									<button
										className='transition-all duration-300 text-left text-base hover:translate-x-2'
										style={{ color: colors.neutral.slateGray }}
										onMouseEnter={(e) => {
											e.currentTarget.style.color = colors.brand.blue;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.color = colors.neutral.slateGray;
										}}>
										AI Search
									</button>
								</li>
								<li>
									<button
										className='transition-all duration-300 text-left text-base hover:translate-x-2'
										style={{ color: colors.neutral.slateGray }}
										onMouseEnter={(e) => {
											e.currentTarget.style.color = colors.brand.blue;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.color = colors.neutral.slateGray;
										}}>
										Globe View
									</button>
								</li>
							</ul>
						</div>

						{/* Company Links */}
						<div className='space-y-4'>
							<h4
								className='text-lg font-bold mb-4'
								style={{ color: colors.brand.navy }}>
								Company
							</h4>
							<ul className='space-y-3'>
								<li>
									<button
										className='transition-all duration-300 text-left text-base hover:translate-x-2'
										style={{ color: colors.neutral.slateGray }}
										onMouseEnter={(e) => {
											e.currentTarget.style.color = colors.brand.green;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.color = colors.neutral.slateGray;
										}}>
										About Us
									</button>
								</li>
								<li>
									<button
										className='transition-all duration-300 text-left text-base hover:translate-x-2'
										style={{ color: colors.neutral.slateGray }}
										onMouseEnter={(e) => {
											e.currentTarget.style.color = colors.brand.green;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.color = colors.neutral.slateGray;
										}}>
										Contact
									</button>
								</li>
								<li>
									<button
										className='transition-all duration-300 text-left text-base hover:translate-x-2'
										style={{ color: colors.neutral.slateGray }}
										onMouseEnter={(e) => {
											e.currentTarget.style.color = colors.brand.green;
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.color = colors.neutral.slateGray;
										}}>
										Privacy Policy
									</button>
								</li>
							</ul>
						</div>
					</div>
				</div>

				{/* Enhanced Bottom Section */}
				<div
					className='border-t mt-12 pt-8 text-center'
					style={{ borderColor: colors.ui.gray200 }}>
					<div
						className='p-4 border backdrop-blur-sm inline-block'
						style={{
							background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}80 0%, ${colors.ui.blue50}20 100%)`,
							borderColor: colors.ui.gray200,
						}}>
						<p
							className='text-sm font-medium'
							style={{ color: colors.neutral.slateGray }}>
							© 2024 Wizlop. All rights reserved.
							<span className='hidden sm:inline'>
								{' '}
								• Intelligent Location Discovery Platform
							</span>
						</p>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default LandingFooter;
