/** @format */

'use client';

import { colors } from '@/app/colors';
import React from 'react';

const HeroBackground: React.FC = () => {
	return (
		<div className='absolute inset-0 overflow-hidden pointer-events-none'>
			{/* Clean geometric patterns without balloons */}
			<div className='absolute inset-0 opacity-10'>
				<div
					className='absolute top-0 left-0 w-full h-full'
					style={{
						background: `
							linear-gradient(45deg, ${colors.brand.blue}10 0%, transparent 50%),
							linear-gradient(-45deg, ${colors.brand.green}10 0%, transparent 50%)
						`,
					}}
				/>
			</div>

			{/* Istanbul Map Outline */}
			<div className='absolute inset-0 flex items-center justify-center opacity-10'>
				<svg
					width='400'
					height='300'
					viewBox='0 0 400 300'
					className='text-current'>
					<path
						d='M50 150 Q100 100 150 120 Q200 140 250 130 Q300 120 350 140 Q380 160 350 180 Q300 200 250 190 Q200 180 150 200 Q100 220 50 180 Z'
						fill='none'
						stroke={colors.brand.blue}
						strokeWidth='2'
						className='animate-pulse'
					/>
					{/* Location markers - square style */}
					{Array.from({ length: 8 }).map((_, i) => (
						<rect
							key={i}
							x={80 + i * 35 - 2}
							y={140 + Math.sin(i) * 20 - 2}
							width='4'
							height='4'
							fill={colors.brand.green}
							className='animate-pulse'
							style={{ animationDelay: `${i * 0.2}s` }}
						/>
					))}
				</svg>
			</div>

			{/* Animated Location Markers - Square Style */}
			<div className='absolute inset-0'>
				{Array.from({ length: 8 }).map((_, i) => (
					<div
						key={i}
						className='absolute w-3 h-3 animate-pulse border'
						style={{
							left: `${25 + ((i * 9) % 50)}%`,
							top: `${35 + ((i * 13) % 30)}%`,
							backgroundColor:
								i % 3 === 0
									? colors.brand.blue
									: i % 3 === 1
									? colors.brand.green
									: colors.supporting.lightBlue,
							borderColor:
								i % 3 === 0
									? colors.brand.blue
									: i % 3 === 1
									? colors.brand.green
									: colors.supporting.lightBlue,
							animationDelay: `${i * 0.4}s`,
							animationDuration: `${2.5 + Math.random()}s`,
						}}
					/>
				))}
			</div>
		</div>
	);
};

export default HeroBackground;
