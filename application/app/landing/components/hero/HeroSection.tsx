/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import React from 'react';
import HeroBackground from './HeroBackground';
import HeroBranding from './HeroBranding';

interface HeroSectionProps {
	onGetStarted: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
	// Use the new viewport height hook for hero section
	const { height, minHeight, maxHeight } = useViewportHeight('hero');

	return (
		<div
			className='relative overflow-hidden bg-transparent viewport-height-full'
			style={
				{
					height,
					minHeight,
					maxHeight,
					boxSizing: 'border-box',
				} as React.CSSProperties
			}>
			{/* Background Features */}
			<HeroBackground />

			{/* Main Content Container */}
			<div className='relative'>
				<HeroBranding onGetStarted={onGetStarted} />
			</div>

			{/* Scroll Indicator - Only show on larger screens */}
			<div className='absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce hidden sm:block'>
				<div
					className='w-5 h-8 sm:w-6 sm:h-10 border-2 rounded-full flex justify-center'
					style={{ borderColor: colors.brand.blue }}>
					<div
						className='w-1 h-2 sm:h-3 rounded-full mt-1 sm:mt-2 animate-pulse'
						style={{ background: colors.brand.blue }}></div>
				</div>
			</div>
		</div>
	);
};

export default HeroSection;
