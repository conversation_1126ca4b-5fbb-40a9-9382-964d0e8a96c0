/** @format */

'use client';

import React from 'react';

interface WaveTransitionProps {
	isActive: boolean;
	direction: 'up' | 'down';
	color?: string;
}

const WaveTransition: React.FC<WaveTransitionProps> = ({
	isActive,
	direction,
	color = '#3B82F6',
}) => {
	return (
		<div
			className={`absolute inset-x-0 pointer-events-none transition-all duration-1000 ease-out ${
				direction === 'up' ? 'bottom-0' : 'top-0'
			}`}
			style={{
				height: isActive ? '120px' : '0px', // Increased height for better visibility
				opacity: isActive ? 1 : 0,
				transform: isActive
					? 'translateY(0)'
					: direction === 'up'
					? 'translateY(80px)' // Reduced distance for smoother animation
					: 'translateY(-80px)',
			}}>
			<svg
				className='w-full h-full'
				viewBox='0 0 1200 120'
				preserveAspectRatio='none'>
				<defs>
					<style>
						{`
							.wave-path {
								animation: waveFlow 4s ease-in-out infinite;
								transform-origin: center;
							}
							.wave-path-2 {
								animation: waveFlow 4s ease-in-out infinite;
								animation-delay: 0.5s;
								transform-origin: center;
							}
							@keyframes waveFlow {
								0%, 100% {
									transform: translateX(0) scaleY(1);
								}
								25% {
									transform: translateX(-10px) scaleY(1.05);
								}
								50% {
									transform: translateX(0) scaleY(0.95);
								}
								75% {
									transform: translateX(10px) scaleY(1.05);
								}
							}
						`}
					</style>
				</defs>
				<path
					className='wave-path'
					d={
						direction === 'up'
							? 'M0,120 C150,100 350,0 600,10 C850,20 1050,100 1200,80 L1200,120 Z'
							: 'M0,0 C150,20 350,120 600,110 C850,100 1050,20 1200,40 L1200,0 Z'
					}
					fill={color}
					fillOpacity='0.4'
				/>
				<path
					className='wave-path-2'
					d={
						direction === 'up'
							? 'M0,120 C300,80 600,20 1200,60 L1200,120 Z'
							: 'M0,0 C300,40 600,100 1200,60 L1200,0 Z'
					}
					fill={color}
					fillOpacity='0.6'
				/>
			</svg>
		</div>
	);
};

export default WaveTransition;
