/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';
import {
	FiArrowRight,
	FiMapPin,
	FiMessageCircle,
	FiSearch,
	FiSend,
	FiStar,
	FiTarget,
	FiZap,
} from 'react-icons/fi';

interface AIShowcaseProps {
	onGetStarted: () => void;
}

interface DemoStep {
	icon: React.ReactNode;
	title: string;
	description: string;
	isActive: boolean;
}

const AIShowcase: React.FC<AIShowcaseProps> = ({ onGetStarted }) => {
	// Use viewport height for responsive section sizing
	const { screenSize, minHeight, padding } = useViewportHeight('section');

	const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
	const [currentStep, setCurrentStep] = useState(0);
	const [userText, setUserText] = useState('');
	const [aiText, setAIText] = useState('');
	const [isTyping, setIsTyping] = useState(false);
	const [showUserMessage, setShowUserMessage] = useState(false);
	const [showAIResponse, setShowAIResponse] = useState(false);
	const [showResults, setShowResults] = useState(false);
	const [isManualControl, setIsManualControl] = useState(false);
	const [inputText, setInputText] = useState('');
	const [isTypingInInput, setIsTypingInInput] = useState(false);

	const scenarios = LANDING_PAGE_DATA.aiDemoScenarios;
	const currentScenario = scenarios[currentScenarioIndex];

	// Animation cycle with manual control support
	useEffect(() => {
		let isCancelled = false;
		let timeoutIds: NodeJS.Timeout[] = [];

		const createTimeout = (
			callback: () => void,
			delay: number
		): Promise<void> => {
			return new Promise((resolve) => {
				const timeoutId = setTimeout(() => {
					if (!isCancelled) {
						callback();
						resolve();
					}
				}, delay);
				timeoutIds.push(timeoutId);
			});
		};

		const animationCycle = async () => {
			try {
				if (isCancelled) return;

				// Reset states
				setShowUserMessage(false);
				setShowAIResponse(false);
				setShowResults(false);
				setUserText('');
				setAIText('');
				setInputText('');
				setIsTyping(false);
				setIsTypingInInput(false);
				setCurrentStep(0);

				// Wait before starting
				await createTimeout(() => {}, 1000);
				if (isCancelled) return;

				// Type in input box first
				setIsTypingInInput(true);
				for (let i = 0; i <= currentScenario.userQuery.length; i++) {
					if (isCancelled) return;
					setInputText(currentScenario.userQuery.slice(0, i));
					await createTimeout(() => {}, 50);
				}

				// Wait a moment, then "send" the message
				await createTimeout(() => {}, 800);
				if (isCancelled) return;

				setIsTypingInInput(false);
				setInputText(''); // Clear input
				setShowUserMessage(true);
				setUserText(currentScenario.userQuery); // Show full message in chat

				// Wait and show processing
				await createTimeout(() => {}, 800);
				if (isCancelled) return;

				setIsTyping(true);

				// Cycle through processing steps
				for (
					let step = 0;
					step < currentScenario.processingSteps.length;
					step++
				) {
					if (isCancelled) return;
					setCurrentStep(step);
					await createTimeout(() => {}, 1000);
				}

				// Type AI response
				if (isCancelled) return;
				setIsTyping(false);
				setShowAIResponse(true);

				for (let i = 0; i <= currentScenario.aiResponse.length; i++) {
					if (isCancelled) return;
					setAIText(currentScenario.aiResponse.slice(0, i));
					await createTimeout(() => {}, 30);
				}

				// Show results
				await createTimeout(() => {}, 800);
				if (isCancelled) return;
				setShowResults(true);

				// Wait before next cycle (only if not manually controlled)
				if (!isManualControl && !isCancelled) {
					await createTimeout(() => {
						setCurrentScenarioIndex((prev) => (prev + 1) % scenarios.length);
					}, 4000);
				}
			} catch (error) {
				console.error('Animation cycle error:', error);
				// Reset to prevent getting stuck
				setIsTyping(false);
				setIsTypingInInput(false);
				setShowUserMessage(false);
				setShowAIResponse(false);
				setShowResults(false);
				setInputText('');
			}
		};

		animationCycle();

		// Cleanup function
		return () => {
			isCancelled = true;
			timeoutIds.forEach((id) => clearTimeout(id));
		};
	}, [
		currentScenarioIndex,
		currentScenario.userQuery,
		currentScenario.aiResponse,
	]); // Only re-run when scenario actually changes

	// Handle manual dot clicks
	const handleDotClick = (index: number) => {
		if (index !== currentScenarioIndex) {
			setIsManualControl(true);
			setCurrentScenarioIndex(index);

			// Reset manual control after the current animation completes to resume auto-cycling
			setTimeout(() => {
				setIsManualControl(false);
			}, 12000); // 12 seconds to allow full animation cycle before resuming auto-cycle
		}
	};

	// Auto-reset manual control to ensure continuous cycling
	useEffect(() => {
		if (isManualControl) {
			const resetTimer = setTimeout(() => {
				setIsManualControl(false);
			}, 15000); // Reset after 15 seconds to ensure cycling continues

			return () => clearTimeout(resetTimer);
		}
	}, [isManualControl]);

	// Processing steps for scenarios
	const processingSteps: DemoStep[] = [
		{
			icon: React.createElement(FiTarget, { className: 'w-4 h-4' }),
			title: 'Understanding Context',
			description: 'AI analyzes your natural language query',
			isActive: currentStep >= 0 && isTyping,
		},
		{
			icon: React.createElement(FiMapPin, { className: 'w-4 h-4' }),
			title: 'Location Processing',
			description: 'Identifying geographic preferences',
			isActive: currentStep >= 1 && isTyping,
		},
		{
			icon: React.createElement(FiSearch, { className: 'w-4 h-4' }),
			title: 'Smart Filtering',
			description: 'Applying intelligent filters and ranking',
			isActive: currentStep >= 2 && isTyping,
		},
		{
			icon: React.createElement(FiZap, { className: 'w-4 h-4' }),
			title: 'Personalized Results',
			description: 'Delivering tailored recommendations',
			isActive: currentStep >= 3 && isTyping,
		},
	];

	return (
		<div
			className='bg-transparent viewport-section'
			style={{
				minHeight,
				maxHeight: 'calc(100vh - 100px)',
				paddingLeft: 0,
				paddingRight: 0,
				paddingTop: padding,
				paddingBottom: '120px',
				overflow: 'hidden',
			}}>
			{/* Container with height constraints */}
			<div className='h-full flex flex-col'>
				{/* Compact Header */}
				<div className='relative mb-6 lg:mb-8 flex-shrink-0'>
					{/* Background accent shapes */}
					<div className='absolute inset-0 opacity-10'>
						<div
							className='absolute top-0 right-0 w-20 h-20'
							style={{
								background: `radial-gradient(circle, ${colors.brand.green}30 0%, transparent 70%)`,
							}}
						/>
						<div
							className='absolute top-4 left-1/3 w-12 h-12 rotate-12'
							style={{
								background: `linear-gradient(45deg, ${colors.brand.blue}25 0%, transparent 70%)`,
							}}
						/>
					</div>

					{/* Compact Header - Badge and Title only */}
					<div className='relative text-center lg:text-left'>
						<div className='flex justify-center lg:justify-start mb-4'>
							<div
								className='inline-flex items-center space-x-2 rounded-full px-4 py-2 border backdrop-blur-sm'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.blue50}80 0%, ${colors.ui.green50}80 100%)`,
									borderColor: colors.ui.gray200,
									boxShadow: '0 4px 16px rgba(128, 237, 153, 0.08)',
								}}>
								<FiMessageCircle
									className='w-4 h-4'
									style={{ color: colors.brand.green }}
								/>
								<span
									className='text-sm font-medium'
									style={{ color: colors.neutral.textBlack }}>
									{LANDING_PAGE_DATA.sectionHeaders.aiDemo.badge}
								</span>
							</div>
						</div>

						<h2 className='text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight'>
							<span style={{ color: colors.brand.navy }}>
								{LANDING_PAGE_DATA.sectionHeaders.aiDemo.title}
							</span>{' '}
							<span
								className='text-transparent bg-clip-text'
								style={{
									backgroundImage: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
								}}>
								{LANDING_PAGE_DATA.sectionHeaders.aiDemo.subtitle}
							</span>
						</h2>
					</div>
				</div>

				{/* Main Content Area - Three Column Layout */}
				<div
					className='flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6 min-h-0'
					style={{ maxHeight: 'calc(100vh - 300px)' }}>
					{' '}
					{/* Respect wave vertex + padding */}
					{/* Left Column - Info Panel */}
					<div className='lg:col-span-1 flex flex-col'>
						<div
							className='p-4 lg:p-6 border backdrop-blur-sm h-full'
							style={{
								background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}90 0%, ${colors.ui.blue50}30 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
							}}>
							<h3
								className='text-lg font-bold mb-3'
								style={{ color: colors.brand.navy }}>
								AI-Powered Search
							</h3>
							<p
								className='text-sm leading-relaxed font-medium mb-4'
								style={{ color: colors.neutral.slateGray }}>
								{LANDING_PAGE_DATA.sectionHeaders.aiDemo.description}
							</p>

							{/* AI Features */}
							<div className='space-y-3'>
								<div className='flex items-center space-x-2'>
									<div
										className='w-6 h-6 flex items-center justify-center'
										style={{ background: `${colors.brand.blue}20` }}>
										<FiTarget
											className='w-3 h-3'
											style={{ color: colors.brand.blue }}
										/>
									</div>
									<span
										className='text-xs font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Natural language
									</span>
								</div>
								<div className='flex items-center space-x-2'>
									<div
										className='w-6 h-6 flex items-center justify-center'
										style={{ background: `${colors.brand.green}20` }}>
										<FiZap
											className='w-3 h-3'
											style={{ color: colors.brand.green }}
										/>
									</div>
									<span
										className='text-xs font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Instant results
									</span>
								</div>
								<div className='flex items-center space-x-2'>
									<div
										className='w-6 h-6 flex items-center justify-center'
										style={{ background: `${colors.brand.blue}20` }}>
										<FiSearch
											className='w-3 h-3'
											style={{ color: colors.brand.blue }}
										/>
									</div>
									<span
										className='text-xs font-medium'
										style={{ color: colors.neutral.slateGray }}>
										Smart filtering
									</span>
								</div>
							</div>
						</div>
					</div>
					{/* Middle Column - AI Assistant Demo */}
					<div className='lg:col-span-2 flex flex-col min-h-0'>
						{/* Scenario Selector */}
						<div className='flex justify-center mb-3 flex-shrink-0'>
							<div className='flex space-x-2'>
								{scenarios.map((_, index) => (
									<button
										key={index}
										onClick={() => handleDotClick(index)}
										className='w-3 h-3 transition-all duration-500 hover:scale-110 cursor-pointer focus:outline-none'
										style={{
											background:
												currentScenarioIndex === index
													? colors.brand.blue
													: colors.ui.gray300,
											transform:
												currentScenarioIndex === index
													? 'scale(1.3)'
													: 'scale(1)',
											boxShadow:
												currentScenarioIndex === index
													? `0 0 8px ${colors.brand.blue}40`
													: 'none',
										}}
										aria-label={`Switch to example ${index + 1}`}
									/>
								))}
							</div>
						</div>

						{/* Demo Container */}
						<div className='flex-1 min-h-0 flex flex-col'>
							{/* Chat Interface */}
							<div
								className='p-4 border flex-1 min-h-0 flex flex-col'
								style={{
									background: `linear-gradient(135deg, ${colors.ui.blue50}20 0%, ${colors.neutral.cloudWhite} 100%)`,
									borderColor: colors.ui.gray200,
									boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
								}}>
								{/* Chat Header */}
								<div
									className='flex items-center space-x-3 mb-4 pb-3 border-b flex-shrink-0'
									style={{ borderColor: colors.ui.gray200 }}>
									<div
										className='w-8 h-8 flex items-center justify-center'
										style={{ background: colors.brand.blue }}>
										<FiMessageCircle className='w-4 h-4 text-white' />
									</div>
									<div>
										<h3
											className='text-sm font-semibold'
											style={{ color: colors.neutral.textBlack }}>
											AI Assistant
										</h3>
										<p
											className='text-xs'
											style={{ color: colors.neutral.slateGray }}>
											{currentScenario.id === 'cozy-cafe'
												? '☕ Finding a Work Cafe'
												: '🍽️ Planning a Romantic Dinner'}
										</p>
									</div>
								</div>

								{/* Chat Messages */}
								<div className='space-y-3 flex-1 min-h-0 overflow-hidden'>
									{/* User Message */}
									{showUserMessage && (
										<div className='flex justify-end'>
											<div
												className='max-w-[80%] p-3'
												style={{
													background: colors.brand.blue,
													color: 'white',
												}}>
												<p className='text-sm'>
													{userText}
													<span className='animate-pulse'>|</span>
												</p>
											</div>
										</div>
									)}

									{/* AI Thinking Process - Small Cards in Chat */}
									{isTyping && (
										<div className='flex justify-start'>
											<div
												className='p-3 max-w-md'
												style={{
													background: colors.ui.blue50,
													borderColor: colors.ui.blue200,
												}}>
												<div className='space-y-2'>
													<div className='flex items-center space-x-2 mb-2'>
														<div className='w-2 h-2 bg-blue-400 animate-pulse'></div>
														<span className='text-xs font-medium text-blue-600'>
															AI Processing...
														</span>
													</div>
													<div className='grid grid-cols-4 gap-1'>
														{processingSteps.map((step, index) => (
															<div
																key={index}
																className={`p-1.5 border text-xs transition-all duration-500 ${
																	step.isActive ? 'scale-105' : ''
																}`}
																style={{
																	background: step.isActive
																		? `linear-gradient(135deg, ${colors.brand.blue}15 0%, ${colors.brand.green}15 100%)`
																		: colors.ui.gray50,
																	borderColor: step.isActive
																		? colors.brand.blue
																		: colors.ui.gray200,
																	opacity: step.isActive ? 1 : 0.4,
																}}>
																<div className='flex flex-col items-center text-center space-y-1'>
																	<div
																		className='w-4 h-4 flex items-center justify-center'
																		style={{
																			background: step.isActive
																				? colors.brand.blue
																				: colors.ui.gray300,
																		}}>
																		{React.isValidElement(step.icon)
																			? React.cloneElement(
																					step.icon as React.ReactElement,
																					{
																						style: {
																							color: 'white',
																							fontSize: '10px',
																						},
																					}
																			  )
																			: step.icon}
																	</div>
																	<span
																		className='font-medium leading-tight'
																		style={{
																			color: step.isActive
																				? colors.neutral.textBlack
																				: colors.neutral.slateGray,
																			fontSize: '8px',
																		}}>
																		{step.title}
																	</span>
																</div>
															</div>
														))}
													</div>
												</div>
											</div>
										</div>
									)}

									{/* AI Response */}
									{showAIResponse && (
										<div className='flex justify-start'>
											<div
												className='max-w-[80%] p-3'
												style={{
													background: colors.ui.gray100,
												}}>
												<p
													className='text-sm'
													style={{ color: colors.neutral.textBlack }}>
													{aiText}
													<span className='animate-pulse'>|</span>
												</p>
											</div>
										</div>
									)}
								</div>

								{/* Animated Input Box */}
								<div
									className='mt-3 pt-3 border-t flex-shrink-0'
									style={{ borderColor: colors.ui.gray200 }}>
									<div className='relative'>
										<input
											type='text'
											value={inputText}
											readOnly
											placeholder={
												isTypingInInput ? '' : 'Type your query here...'
											}
											className='w-full p-3 pr-12 border text-sm transition-all cursor-default'
											style={{
												backgroundColor: colors.neutral.cloudWhite,
												borderColor: isTypingInInput
													? colors.brand.blue
													: colors.ui.gray300,
												color: colors.neutral.textBlack,
												boxShadow: isTypingInInput
													? `0 0 0 2px ${colors.brand.blue}20`
													: 'none',
											}}
										/>
										<div
											className='absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 flex items-center justify-center transition-all duration-200'
											style={{
												background:
													inputText.length > 0
														? colors.brand.blue
														: colors.ui.gray300,
												color: 'white',
											}}>
											<FiSend className='w-4 h-4' />
										</div>
										{isTypingInInput && (
											<div className='absolute right-14 top-1/2 transform -translate-y-1/2'>
												<div className='flex space-x-1'>
													<div className='w-1 h-1 bg-gray-400 animate-pulse'></div>
													<div
														className='w-1 h-1 bg-gray-400 animate-pulse'
														style={{ animationDelay: '0.2s' }}></div>
													<div
														className='w-1 h-1 bg-gray-400 animate-pulse'
														style={{ animationDelay: '0.4s' }}></div>
												</div>
											</div>
										)}
									</div>
								</div>
							</div>
						</div>
					</div>
					{/* Right Column - Results */}
					<div className='lg:col-span-1 flex flex-col'>
						{/* Results Header - Left Aligned */}
						{showResults && (
							<div className='mb-4'>
								<div className='flex items-center space-x-2 mb-2'>
									<div
										className='w-6 h-6 flex items-center justify-center'
										style={{ background: colors.brand.green }}>
										<FiStar className='w-3 h-3 text-white' />
									</div>
									<h3
										className='text-sm font-semibold'
										style={{ color: colors.neutral.textBlack }}>
										Perfect Matches Found
									</h3>
								</div>
								<p
									className='text-xs'
									style={{ color: colors.neutral.slateGray }}>
									AI found 3 locations that match your preferences
								</p>
							</div>
						)}

						{/* Results Cards - Vertical Stack */}
						{showResults && (
							<div className='flex-1 space-y-3'>
								{currentScenario.searchResults
									.slice(0, 3)
									.map((result, index) => {
										// Different light blue colors for each card
										const cardColors = [
											colors.ui.blue50,
											colors.supporting.lightBlue + '20',
											colors.ui.blue100,
										];
										const borderColors = [
											colors.ui.blue200,
											colors.supporting.lightBlue + '40',
											colors.ui.blue200,
										];

										return (
											<div
												key={index}
												className='p-3 border transition-all duration-300'
												style={{
													background: cardColors[index % 3],
													borderColor: borderColors[index % 3],
													animationDelay: `${index * 200}ms`,
													animation: showResults
														? 'slideInUp 0.6s ease-out forwards'
														: 'none',
												}}>
												<div className='flex items-start justify-between mb-2'>
													<h4
														className='font-medium text-xs'
														style={{ color: colors.neutral.textBlack }}>
														{result.name}
													</h4>
													<div className='flex items-center space-x-1'>
														<FiStar
															className='w-3 h-3'
															style={{ color: colors.brand.green }}
														/>
														<span
															className='text-xs font-medium'
															style={{ color: colors.neutral.textBlack }}>
															{result.rating}
														</span>
													</div>
												</div>
												<p
													className='text-xs mb-2'
													style={{ color: colors.neutral.slateGray }}>
													{result.type} • {result.distance}
												</p>
												<div className='flex flex-wrap gap-1'>
													{result.features
														.slice(0, 2)
														.map((feature, featureIndex) => (
															<span
																key={featureIndex}
																className='px-2 py-1 text-xs'
																style={{
																	background: colors.ui.blue200,
																	color: colors.brand.blue,
																}}>
																{feature}
															</span>
														))}
												</div>
											</div>
										);
									})}
							</div>
						)}
					</div>
				</div>

				{/* Call to Action - Below main content */}
				<div className='flex-shrink-0 mt-6'>
					<div className='text-center'>
						<button
							onClick={onGetStarted}
							className='inline-flex items-center space-x-2 px-6 py-3 font-semibold text-white transition-all duration-300 hover:scale-105 hover:shadow-lg'
							style={{
								background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
								boxShadow: '0 4px 20px rgba(51, 194, 255, 0.3)',
							}}>
							<span>Try AI Search Now</span>
							<FiArrowRight className='w-4 h-4' />
						</button>
						<p
							className='text-sm mt-3'
							style={{ color: colors.neutral.slateGray }}>
							Experience the power of AI-driven location discovery
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

export default AIShowcase;
