/** @format */

'use client';

import { useEffect, useRef, useState } from 'react';

// Animation types for different section transitions
export type AnimationType =
	| 'wave-wash'
	| 'slide-diagonal'
	| 'fade-scale'
	| 'ripple-effect'
	| 'geometric-morph';

// Configuration for scroll animations
export interface ScrollAnimationConfig {
	threshold: number;
	rootMargin: string;
	animationType: AnimationType;
	duration: number;
	delay: number;
	easing: string;
}

// Default animation configurations
export const ANIMATION_CONFIGS: Record<string, ScrollAnimationConfig> = {
	heroToFeatures: {
		threshold: 0.1, // Reduced from 0.3 to trigger earlier
		rootMargin: '20% 0px -20% 0px', // Increased top margin to trigger earlier
		animationType: 'wave-wash',
		duration: 800, // Reduced from 1200 to make animation faster
		delay: 0,
		easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
	},
	featureToFeature: {
		threshold: 0.2, // Reduced from 0.4 to trigger earlier
		rootMargin: '15% 0px -15% 0px', // Increased margins to trigger earlier
		animationType: 'slide-diagonal',
		duration: 500, // Reduced from 800 to make animation faster
		delay: 50, // Reduced from 100 to make animation faster
		easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
	},
	featureToFooter: {
		threshold: 0.1, // Reduced from 0.2 to trigger earlier
		rootMargin: '10% 0px -25% 0px', // Adjusted margins to trigger earlier
		animationType: 'fade-scale',
		duration: 600, // Reduced from 1000 to make animation faster
		delay: 100, // Reduced from 200 to make animation faster
		easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
	},
};

// Hook for scroll-triggered animations
export const useScrollAnimation = (
	config: ScrollAnimationConfig,
	triggerOnce: boolean = true
) => {
	const elementRef = useRef<HTMLDivElement>(null);
	const [isVisible, setIsVisible] = useState(false);
	const [hasTriggered, setHasTriggered] = useState(false);

	useEffect(() => {
		const element = elementRef.current;
		if (!element) return;

		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						if (!triggerOnce || !hasTriggered) {
							setIsVisible(true);
							setHasTriggered(true);
						}
					} else if (!triggerOnce) {
						setIsVisible(false);
					}
				});
			},
			{
				threshold: config.threshold,
				rootMargin: config.rootMargin,
			}
		);

		observer.observe(element);

		return () => {
			observer.unobserve(element);
		};
	}, [config.threshold, config.rootMargin, triggerOnce, hasTriggered]);

	return { elementRef, isVisible, hasTriggered };
};

// Hook for section transition animations
export const useSectionTransition = (
	sectionId: string,
	animationType: AnimationType = 'slide-diagonal'
) => {
	const config = ANIMATION_CONFIGS.featureToFeature;
	config.animationType = animationType;

	const { elementRef, isVisible } = useScrollAnimation(config);
	const [animationState, setAnimationState] = useState<
		'idle' | 'entering' | 'visible'
	>('idle');

	useEffect(() => {
		if (isVisible && animationState === 'idle') {
			setAnimationState('entering');
			setTimeout(() => {
				setAnimationState('visible');
			}, config.duration);
		}
	}, [isVisible, animationState, config.duration]);

	return { elementRef, animationState, isVisible };
};

// CSS animation classes generator
export const getAnimationClasses = (
	animationType: AnimationType,
	state: 'idle' | 'entering' | 'visible'
): string => {
	const baseClasses = 'transition-all duration-1000 ease-out';

	switch (animationType) {
		case 'wave-wash':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform translate-y-8 scale-95'
					: state === 'entering'
					? 'opacity-70 transform translate-y-4 scale-98'
					: 'opacity-100 transform translate-y-0 scale-100'
			}`;

		case 'slide-diagonal':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform translate-x-12 translate-y-8 rotate-1'
					: state === 'entering'
					? 'opacity-70 transform translate-x-6 translate-y-4 rotate-0.5'
					: 'opacity-100 transform translate-x-0 translate-y-0 rotate-0'
			}`;

		case 'fade-scale':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform scale-90'
					: state === 'entering'
					? 'opacity-50 transform scale-95'
					: 'opacity-100 transform scale-100'
			}`;

		case 'ripple-effect':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform scale-110'
					: state === 'entering'
					? 'opacity-70 transform scale-105'
					: 'opacity-100 transform scale-100'
			}`;

		case 'geometric-morph':
			return `${baseClasses} ${
				state === 'idle'
					? 'opacity-0 transform skew-x-3 skew-y-1 scale-95'
					: state === 'entering'
					? 'opacity-70 transform skew-x-1 skew-y-0.5 scale-98'
					: 'opacity-100 transform skew-x-0 skew-y-0 scale-100'
			}`;

		default:
			return baseClasses;
	}
};

// Animation utility functions only - React components moved to separate files
