/** @format */

import React from 'react';

// ===== CORE CHAT TYPES =====

export interface Message {
	text: string;
	isUser: boolean;
	isLoading?: boolean;
}

export interface Session {
	id: string;
	title: string;
}

export interface TopCandidates {
	locations?: Array<Record<string, unknown>>;
	[key: string]: unknown;
}

export interface ExtractedLocation extends Record<string, unknown> {
	name: string;
	lat?: number;
	lng?: number;
	latitude?: number;
	longitude?: number;
	confidence: number;
	walk_route_distance_m?: number;
	address?: string;
	messageIndex?: number; // Track which message this came from
}

// ===== STATE TYPES =====

export interface ChatUIState {
	isLeftOpen: boolean;
	isRightOpen: boolean;
	dropdownIndex: number | null;
	showScrollButton: boolean;
	isClient: boolean;
	isGlobeTransitioning: boolean;
	// Optional setters for enhanced state
	setIsLeftOpen?: (open: boolean) => void;
	setIsRightOpen?: (open: boolean) => void;
	setDropdownIndex?: (index: number | null) => void;
	setShowScrollButton?: (show: boolean) => void;
	setIsGlobeTransitioning?: (transitioning: boolean) => void;
}

export interface ChatState {
	messages: Message[];
	newMessage: string;
	sessionId: string | null;
	sessionList: Session[];
	topCandidates: TopCandidates | null;
	allExtractedLocations: ExtractedLocation[]; // All POIs from the entire conversation
	isSending: boolean;
}

export interface LocationState {
	userLocation: { lat: number; lng: number } | null;
	locationError: string | null;
	locationLoading: boolean;
	requestAutoLocation: () => Promise<void>;
	markSetupComplete: (choice: 'auto' | 'manual' | 'none') => void;
	needsLocationSetup: () => boolean;
	isInitialized: boolean;
}

// ===== ACTION TYPES =====

export interface ChatActions {
	sendMessage: (message: string) => Promise<void>;
	startNewChat: () => Promise<void>;
	loadOldMessages: (sessionId: string) => Promise<void>;
	handleDeleteSession: (sessionId: string) => Promise<void>;
	scrollToBottom: () => void;
	handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
	toggleDropdown: (index: number) => void;
	handleGlobeTransition: () => void;
	refreshSessionTitles: () => void;
}

// ===== CONTEXT TYPES =====

export interface ChatContextValue {
	// State
	chatState: ChatState;
	uiState: ChatUIState;
	locationState: LocationState;
	session: any; // Next.js session

	// Actions
	actions: ChatActions;

	// Refs
	inputRef: React.RefObject<HTMLTextAreaElement>;
	messagesEndRef: React.RefObject<HTMLDivElement>;
	messagesContainerRef: React.RefObject<HTMLDivElement>;

	// Setters (for components that need direct access)
	setChatState: React.Dispatch<React.SetStateAction<ChatState>>;
	setUIState: React.Dispatch<React.SetStateAction<ChatUIState>>;
	setSessionList: React.Dispatch<React.SetStateAction<Session[]>>;

	// Location Setup
	showLocationSetup: boolean;
	handleLocationSetupComplete: (choice: 'auto' | 'manual' | 'none') => void;
}

// ===== COMPONENT PROP TYPES =====

export interface TopBarProps {
	isLeftOpen: boolean;
	isRightOpen: boolean;
	setIsLeftOpen: (open: boolean) => void;
	setIsRightOpen: (open: boolean) => void;
	startNewChat: () => Promise<void>;
	userLocation: { lat: number; lng: number } | null;
	locationError: string | null;
	locationLoading: boolean;
	requestAutoLocation: () => Promise<void>;
}

export interface BottomBarProps {
	newMessage: string;
	handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
	inputRef: React.RefObject<HTMLTextAreaElement>;
	isSending: boolean;
	hasMessages: boolean;
}

export interface MessageAreaProps {
	messages: Message[];
	showScrollButton: boolean;
	scrollToBottom: () => void;
	messagesContainerRef: React.RefObject<HTMLDivElement>;
	messagesEndRef: React.RefObject<HTMLDivElement>;
	onShowMap: () => void;
	extractedLocations?: TopCandidates | null;
}

export interface LeftSidebarProps {
	isOpen: boolean;
	onClose: () => void;
	sessionList: Session[];
	activeSessionId: string | null;
	dropdownIndex: number | null;
	onStartNewChat: () => Promise<void>;
	onLoadOldMessages: (sessionId: string) => Promise<void>;
	onToggleDropdown: (index: number) => void;
	onDeleteSession: (sessionId: string) => Promise<void>;
}

export interface RightSidebarProps {
	isOpen: boolean;
	onClose: () => void;
	userLocation: { lat: number; lng: number } | null;
	extractedLocations: Array<Record<string, unknown>>;
}

export interface BackgroundGlobeProps {
	isVisible: boolean;
	isTransitioning: boolean;
	onTransitionComplete?: () => void;
}

// ===== HOOK TYPES =====

export interface UseChatSessionProps {
	sessionId: string;
	activeSessionId: string | null;
	onLoadOldMessages: (id: string) => void;
	onDeleteSession: (id: string) => void;
}

export interface UseMessageActionsProps {
	onShowMap: () => void;
}

export interface UseChatMessagesProps {
	messages: Message[];
	isSending: boolean;
	setShowScrollButton: (show: boolean) => void;
}

export interface UseChatSessionsProps {
	sessionList: Session[];
	sessionId: string | null;
	isClient: boolean;
	setSessionList: (
		sessions: Session[] | ((prev: Session[]) => Session[])
	) => void;
}

export interface UseChatSessionsReturn {
	refreshSessionTitles: () => void;
}

export interface UseChatActionsProps {
	sessionId: string | null;
	setSessionId: (id: string | null) => void;
	setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
	setSessionList: React.Dispatch<React.SetStateAction<Session[]>>;
	setTopCandidates: (candidates: TopCandidates | null) => void;
	setAllExtractedLocations: (
		locations:
			| Array<Record<string, unknown>>
			| ((
					prev: Array<Record<string, unknown>>
			  ) => Array<Record<string, unknown>>)
	) => void;
	addExtractedLocations: (
		newLocations: Array<Record<string, unknown>>,
		messageIndex: number
	) => void;
	setIsSending: (sending: boolean) => void;
	setNewMessage: (message: string) => void;
	setIsRightOpen: (open: boolean) => void;
	inputRef: React.RefObject<HTMLTextAreaElement>;
	userLocation: { lat: number; lng: number } | null;
	refreshSessionTitles: () => void;
}
