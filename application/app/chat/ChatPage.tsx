/** @format */

'use client';

import {
	InputArea,
	LeftSidebar,
	MessageArea,
	RightSidebar,
	TopBar,
} from 'app/chat/components';
import { useStreamingChat } from 'app/chat/hooks/useStreamingChat';
import { ChatContextValue } from 'app/chat/types';
import {
	useChatMessages,
	useChatSessions,
	useChatState,
	useChatUI,
} from 'app/chat/useChatHooks';
import {
	LocationSetup,
	useLocationManager,
	useLocationSetup,
} from 'app/shared/locationManager';
import { useSession } from 'next-auth/react';
import React, { createContext, useContext, useMemo } from 'react';

// ===== CONTEXT =====

const ChatContext = createContext<ChatContextValue | null>(null);

export function useChatContext() {
	const context = useContext(ChatContext);
	if (!context) {
		throw new Error('useChatContext must be used within a ChatPageProvider');
	}
	return context;
}

// ===== MAIN CHAT LAYOUT COMPONENT =====

const ChatLayout: React.FC = () => {
	const {
		chatState,
		uiState,
		locationState,
		session,
		actions,
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		setUIState,
		setSessionList,
		showLocationSetup,
		handleLocationSetupComplete,
	} = useChatContext();

	// Memoize extractedLocations to avoid unnecessary re-renders
	const memoizedExtractedLocations = useMemo(
		() => chatState.allExtractedLocations || [],
		[chatState.allExtractedLocations]
	);

	// Debug: Log sidebar open states and messages on each render
	console.log('ChatLayout render:', {
		isLeftOpen: uiState.isLeftOpen,
		isRightOpen: uiState.isRightOpen,
		messagesCount: chatState.messages.length,
		messages: chatState.messages,
	});

	// Load session titles when component mounts (using working implementation)
	const hasLoadedRef = React.useRef(false);
	React.useEffect(() => {
		const loadSessionTitles = async () => {
			if (!session?.user?.id || hasLoadedRef.current) return;

			hasLoadedRef.current = true;

			try {
				const response = await fetch('/api/chat/titles', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ userId: session.user.id }),
				});

				if (response.ok) {
					const data = await response.json();
					if (data.titles && Array.isArray(data.titles)) {
						const sessions = data.titles.map(
							(title: { session_id: string; title: string }) => ({
								id: title.session_id,
								title: title.title || 'Untitled Session',
							})
						);
						setSessionList(sessions);
						console.log('📋 Loaded', sessions.length, 'session titles');
					}
				}
			} catch (error) {
				console.error('Failed to load session titles:', error);
				hasLoadedRef.current = false; // Reset on error to allow retry
			}
		};

		if (session?.user?.id) {
			loadSessionTitles();
		}
	}, [session?.user?.id]);

	// Set page title based on session
	React.useEffect(() => {
		const sessionTitle =
			chatState.sessionList.find((s: any) => s.id === chatState.sessionId)
				?.title || '';
		document.title = sessionTitle ? `Wizlop | ${sessionTitle}` : 'Wizlop';
	}, [chatState.sessionList, chatState.sessionId]);

	return (
		<>
			{/* Location Setup Modal */}
			<LocationSetup
				isOpen={showLocationSetup}
				onComplete={handleLocationSetupComplete}
				pageContext='chat'
				isModal={true}
				title='Location Setup Required'
				subtitle='To provide better location-based responses, we need to know your location or you can set it manually.'
			/>

			{/* Main Chat Interface - Modern Design */}
			<div
				className='h-screen w-full overflow-hidden flex flex-row relative'
				style={{
					isolation: 'isolate',
				}}>
				{/* Left Sidebar */}
				<LeftSidebar
					isOpen={uiState.isLeftOpen}
					onClose={() => setUIState((prev) => ({ ...prev, isLeftOpen: false }))}
					sessionList={chatState.sessionList}
					activeSessionId={chatState.sessionId}
					dropdownIndex={uiState.dropdownIndex}
					onStartNewChat={actions.startNewChat}
					onLoadOldMessages={actions.loadOldMessages}
					onToggleDropdown={(index) =>
						setUIState((prev) => ({ ...prev, dropdownIndex: index }))
					}
					onDeleteSession={actions.handleDeleteSession}
				/>

				{/* Main Content Column */}
				<div className='flex-1 flex flex-col min-w-0'>
					{/* Top Bar */}
					<TopBar
						isLeftOpen={uiState.isLeftOpen}
						isRightOpen={uiState.isRightOpen}
						setIsLeftOpen={(open) => {
							console.log('setIsLeftOpen called with:', open);
							setUIState((prev) => ({ ...prev, isLeftOpen: open }));
						}}
						setIsRightOpen={(open) => {
							console.log('setIsRightOpen called with:', open);
							setUIState((prev) => ({ ...prev, isRightOpen: open }));
						}}
						startNewChat={actions.startNewChat}
						userLocation={locationState.userLocation}
						locationError={locationState.locationError}
						locationLoading={locationState.locationLoading}
						requestAutoLocation={locationState.requestAutoLocation}
					/>

					{/* Message Area */}
					<MessageArea
						messages={chatState.messages}
						showScrollButton={uiState.showScrollButton}
						scrollToBottom={actions.scrollToBottom}
						messagesContainerRef={messagesContainerRef}
						messagesEndRef={messagesEndRef}
						onShowMap={() =>
							setUIState((prev) => ({ ...prev, isRightOpen: true }))
						}
						extractedLocations={chatState.topCandidates}
					/>

					{/* Input Area */}
					<InputArea
						newMessage={chatState.newMessage}
						handleInputChange={actions.handleInputChange}
						handleKeyDown={actions.handleKeyDown}
						inputRef={inputRef}
						isSending={chatState.isSending}
						hasMessages={chatState.messages.length > 0}
					/>
				</div>

				{/* Right Sidebar */}
				<RightSidebar
					isOpen={uiState.isRightOpen}
					onClose={() =>
						setUIState((prev) => ({ ...prev, isRightOpen: false }))
					}
					userLocation={locationState.userLocation}
					extractedLocations={memoizedExtractedLocations}
				/>
			</div>
		</>
	);
};

// ===== MAIN PROVIDER COMPONENT =====

interface ChatPageProviderProps {
	children: React.ReactNode;
}

export function ChatPageProvider({ children }: ChatPageProviderProps) {
	const { data: session } = useSession();

	// Core state hooks (single chatState object)
	const {
		chatState,
		setChatState,
		setMessages,
		setNewMessage,
		setSessionId,
		setSessionList,
		setTopCandidates,
		setAllExtractedLocations,
		addExtractedLocations,
		setIsSending,
	} = useChatState();

	const {
		uiState,
		setUIState,
		setIsLeftOpen,
		setIsRightOpen,
		setDropdownIndex,
		setShowScrollButton,
		setIsGlobeTransitioning,
	} = useChatUI();

	// Location management
	const locationManager = useLocationManager();

	// Map useLocationManager return to LocationState interface
	const locationState = {
		userLocation: locationManager.location
			? {
					lat: locationManager.location.latitude,
					lng: locationManager.location.longitude,
			  }
			: null,
		locationError: locationManager.error,
		locationLoading: locationManager.isLoading,
		requestAutoLocation: locationManager.requestAutoLocation,
		markSetupComplete: locationManager.markSetupComplete,
		needsLocationSetup: locationManager.needsLocationSetup,
		isInitialized: locationManager.isInitialized,
	};

	// Location setup modal
	const { showLocationSetup, handleLocationSetupComplete } = useLocationSetup();

	// Session management
	const handleDeleteSession = async (sessionIdToDelete: string) => {
		if (!session?.user?.id) return;

		try {
			const response = await fetch('/api/chat/session', {
				method: 'DELETE',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					userId: session.user.id,
					sessionId: sessionIdToDelete,
				}),
			});

			if (response.ok) {
				setSessionList((prev) =>
					prev.filter((session) => session.id !== sessionIdToDelete)
				);

				if (chatState.sessionId === sessionIdToDelete) {
					setMessages([]);
					setSessionId(null);
					setTopCandidates(null);
				}
			}
		} catch (error) {
			console.error('Error deleting session:', error);
		}
	};

	// Auto-scroll, input focus, scroll-to-bottom button
	const {
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		scrollToBottom,
		handleInputChange: autoResizeInputChange,
	} = useChatMessages({
		messages: chatState.messages,
		isSending: chatState.isSending,
		setShowScrollButton,
	});

	// Input change handler: only update newMessage
	const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setNewMessage(e.target.value);
		autoResizeInputChange();
	};

	const startNewChat = async () => {
		setMessages([]);
		setNewMessage('');
		setSessionId(null);
	};

	// Streaming chat functionality
	const { sendStreamingMessage } = useStreamingChat();

	// Enhanced UI state with computed properties
	const enhancedUIState = useMemo(
		() => ({
			...uiState,
			// Add any computed properties here if needed
		}),
		[uiState]
	);

	// Session data is now passed through context

	// Load session history (for left sidebar)
	const { refreshSessionTitles } = useChatSessions({
		sessionList: chatState.sessionList,
		sessionId: chatState.sessionId,
		isClient: uiState.isClient,
		setSessionList,
	});

	// Session creation helper
	const createSessionIfNeeded = async () => {
		if (chatState.sessionId) {
			return chatState.sessionId;
		}

		const userId = session?.user?.id;
		if (!userId) {
			throw new Error('User ID is required to create a session');
		}

		try {
			const res = await fetch('/api/chat/session', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ userId }),
			});

			if (!res.ok) {
				let errorMessage = 'Session could not be created.';
				try {
					const errorData = await res.json();
					errorMessage =
						errorData.error?.message || errorData.error || errorMessage;
				} catch {
					errorMessage = `${errorMessage} (${res.status}: ${res.statusText})`;
				}
				throw new Error(errorMessage);
			}

			const data = await res.json();
			const newId = data.sessionId;
			setSessionId(newId);
			setSessionList((prev) => {
				const sessionExists = prev.some((session) => session.id === newId);
				if (!sessionExists) {
					return [{ id: newId, title: '' }, ...prev];
				}
				return prev;
			});
			return newId;
		} catch (err) {
			console.error('❌ Session creation failed', err);
			return null;
		}
	};

	// Main message sending handler
	const handleSendMessage = async () => {
		if (!chatState.newMessage.trim() || chatState.isSending) return;

		const messageText = chatState.newMessage.trim();
		setNewMessage('');
		setIsSending(true);

		try {
			// Create session if needed
			const currentSessionId = await createSessionIfNeeded();
			if (!currentSessionId) {
				throw new Error('Failed to create or get session ID');
			}

			await sendStreamingMessage(
				{
					message: messageText,
					userId: session?.user?.id || 'anonymous',
					sessionId: currentSessionId,
					userLat: locationState.userLocation?.lat || null,
					userLng: locationState.userLocation?.lng || null,
				},
				{
					onMessageUpdate: (content: string) => {
						console.log('🔄 onMessageUpdate called with content:', content);
						// Update the current assistant message
						setMessages((prev) => {
							const newMessages = [...prev];
							const lastMessage = newMessages[newMessages.length - 1];

							if (lastMessage && !lastMessage.isUser) {
								// Update existing assistant message (remove loading state)
								lastMessage.text = content;
								lastMessage.isLoading = false;
								console.log('✏️ Updated existing message:', lastMessage.text);
							} else {
								// Add new assistant message
								newMessages.push({
									text: content,
									isUser: false,
									isLoading: false,
								});
								console.log('➕ Added new message:', content);
							}
							return newMessages;
						});
					},
					onTopCandidatesUpdate: (candidates: any[]) => {
						console.log('📍 Top candidates received:', candidates);
						const topCandidates = { locations: candidates };
						setTopCandidates(topCandidates);
						if (candidates && candidates.length > 0) {
							addExtractedLocations(candidates, chatState.messages.length - 1);
							setUIState((prev) => ({ ...prev, isRightOpen: true }));
						}
					},
					onSessionTitleUpdate: (title: string) => {
						console.log('📋 Session title received:', title);
						const currentSessionId = chatState.sessionId;
						if (currentSessionId) {
							setSessionList((prev) => {
								const sessionExists = prev.some(
									(session) => session.id === currentSessionId
								);
								if (sessionExists) {
									return prev.map((session) =>
										session.id === currentSessionId
											? { ...session, title }
											: session
									);
								} else {
									return [{ id: currentSessionId, title }, ...prev];
								}
							});

							// Refresh session titles from server to ensure consistency
							console.log('🔄 Refreshing session titles from server...');
							// Add a small delay to ensure the session title is saved on the backend
							setTimeout(() => {
								refreshSessionTitles();
							}, 500);
						}
					},
					onComplete: () => {
						console.log('✅ Streaming complete');
						setIsSending(false);
					},
					onError: (error: string) => {
						console.error('Streaming error:', error);
						// Add error message
						setMessages((prev) => [
							...prev,
							{
								text: `Error: ${error}`,
								isUser: false,
							},
						]);
					},
					onProcessingStart: () => {
						// Add user message and placeholder assistant message
						setMessages((prev) => [
							...prev,
							{
								text: messageText,
								isUser: true,
							},
							{
								text: 'Thinking...',
								isUser: false,
								isLoading: true,
							},
						]);
					},
				}
			);
		} catch (error) {
			console.error('Failed to send message:', error);
		} finally {
			setIsSending(false);
		}
	};

	// Key down handler for input
	const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	// Load old messages handler
	const loadOldMessages = async (sessionId: string) => {
		try {
			console.log('🔄 Loading old messages for session:', sessionId);

			const response = await fetch('/api/chat/messages', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					sessionId,
					userId: session?.user?.id,
				}),
			});

			if (!response.ok) {
				throw new Error('Failed to load messages');
			}

			const data = await response.json();

			const loadedMessages = data.messages.map(
				(msg: { content: string; role: string }) => ({
					text: msg.content,
					isUser: msg.role === 'user',
					isLoading: false,
				})
			);

			setMessages(loadedMessages);
			setSessionId(sessionId);

			// Extract ALL POIs from ALL assistant messages in the conversation
			const allExtractedPOIs: Array<Record<string, unknown>> = [];
			data.messages.forEach(
				(
					msg: {
						role: string;
						metadata?: { top_candidates?: Array<Record<string, unknown>> };
					},
					index: number
				) => {
					if (msg.role === 'assistant' && msg.metadata?.top_candidates) {
						const poisWithIndex = msg.metadata.top_candidates.map(
							(poi: Record<string, unknown>) => ({
								...poi,
								messageIndex: index,
							})
						);
						allExtractedPOIs.push(...poisWithIndex);
					}
				}
			);

			console.log(
				'🗺️ Loaded',
				allExtractedPOIs.length,
				'historical locations for session'
			);

			// Set the accumulated POIs
			setAllExtractedLocations(allExtractedPOIs);

			// Set the most recent topCandidates for compatibility
			const lastAssistantMessage = [...data.messages]
				.reverse()
				.find(
					(msg: {
						role: string;
						metadata?: { top_candidates?: Array<Record<string, unknown>> };
					}) => msg.role === 'assistant' && msg.metadata?.top_candidates
				);

			if (lastAssistantMessage?.metadata?.top_candidates) {
				setTopCandidates(lastAssistantMessage.metadata.top_candidates);
			} else {
				setTopCandidates(null);
			}

			// Open right sidebar if there are any extracted locations
			if (allExtractedPOIs.length > 0) {
				console.log('🗺️ Opening right sidebar to show historical locations');
				setUIState((prev) => ({ ...prev, isRightOpen: true }));
			}
		} catch (error) {
			console.error('Error loading old messages:', error);
		}
	};

	// Actions object
	const actions = {
		sendMessage: handleSendMessage,
		handleInputChange,
		handleKeyDown,
		startNewChat,
		scrollToBottom,
		loadOldMessages,
		handleDeleteSession,
		toggleDropdown: (index: number) => {
			setUIState((prev) => ({ ...prev, dropdownIndex: index }));
		},
		handleGlobeTransition: () => {
			setIsGlobeTransitioning(true);
			setTimeout(() => setIsGlobeTransitioning(false), 1000);
		},
		refreshSessionTitles,
	};

	// Context value
	const contextValue: ChatContextValue = {
		chatState,
		uiState: enhancedUIState,
		locationState,
		session,
		actions,
		inputRef,
		messagesEndRef,
		messagesContainerRef,
		setChatState,
		setUIState,
		setSessionList,
		showLocationSetup,
		handleLocationSetupComplete,
	};

	return (
		<ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>
	);
}

// ===== MAIN EXPORT COMPONENT =====

const ChatPageContent: React.FC = () => {
	return (
		<ChatPageProvider>
			<ChatLayout />
		</ChatPageProvider>
	);
};

export default ChatPageContent;
