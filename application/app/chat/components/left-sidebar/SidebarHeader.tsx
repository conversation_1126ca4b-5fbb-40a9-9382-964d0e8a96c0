/** @format */

import { colors } from 'app/colors';
import Image from 'next/image';
import React from 'react';
import { FaBars } from 'react-icons/fa';

interface SidebarHeaderProps {
	onClose: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ onClose }) => {
	return (
		<div
			className='w-full h-16 px-4 flex justify-between items-center'
			style={{
				borderBottom: `1px solid ${colors.ui.gray200}`,
				background: 'transparent',
				boxSizing: 'border-box',
				height: '64px',
				minHeight: '64px',
				maxHeight: '64px',
				overflow: 'hidden',
			}}>
			<div className='flex items-center gap-3'>
				<div className='w-9 h-9 relative'>
					<Image
						src='/logo/512x512.png'
						alt='Wizlop Logo'
						width={36}
						height={36}
						className='object-contain'
					/>
				</div>
				<div className='flex flex-col justify-center'>
					<h2
						className='text-base font-bold leading-none tracking-tight'
						style={{
							color: colors.brand.navy,
							fontFamily:
								'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
							lineHeight: '1.2',
						}}>
						Wizlop
					</h2>
					<p
						className='text-xs leading-none font-medium opacity-70'
						style={{
							color: colors.brand.blue,
							fontFamily:
								'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
							lineHeight: '1.2',
						}}>
						AI Assistant
					</p>
				</div>
			</div>
			<button
				onClick={onClose}
				className='p-2 transition-all duration-200 hover:scale-110 border'
				style={{
					color: colors.neutral.slateGray,
					backgroundColor: 'transparent',
					borderColor: colors.ui.gray200,
					boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
				}}
				title='Close sidebar'
				onMouseEnter={(e) => {
					e.currentTarget.style.backgroundColor = colors.ui.gray100;
					e.currentTarget.style.color = colors.brand.blue;
					e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}40`;
				}}
				onMouseLeave={(e) => {
					e.currentTarget.style.backgroundColor = 'transparent';
					e.currentTarget.style.color = colors.neutral.slateGray;
					e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.05)';
				}}>
				<FaBars className='w-4 h-4' />
			</button>
		</div>
	);
};

export default SidebarHeader;
