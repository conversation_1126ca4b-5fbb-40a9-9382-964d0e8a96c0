/** @format */

import { IconButton } from '@/app/chat/components/ui';
import { Session } from '@/app/chat/types';
import { colors } from '@/app/colors';
import React, { useState } from 'react';
import { FaPlus, FaSearch } from 'react-icons/fa';
import ChatSessionItem from './ChatSessionItem';
import SearchOverlay from './SearchOverlay';

interface ChatHistorySectionProps {
	sessionList: Session[];
	activeSessionId: string | null;
	dropdownIndex: number | null;
	onStartNewChat: () => void;
	onLoadOldMessages: (id: string) => void;
	onToggleDropdown: (index: number) => void;
	onDeleteSession: (id: string) => void;
}

const ChatHistorySection: React.FC<ChatHistorySectionProps> = ({
	sessionList,
	activeSessionId,
	dropdownIndex,
	onStartNewChat,
	onLoadOldMessages,
	onToggleDropdown,
	onDeleteSession,
}) => {
	const [isSearchOpen, setIsSearchOpen] = useState(false);

	const handleCloseDropdown = () => {
		onToggleDropdown(-1);
	};

	return (
		<>
			<div className='w-full flex-1 px-3 py-4 flex flex-col'>
				<div className='flex-1 flex flex-col'>
					{/* Header Section */}
					<div className='flex items-center justify-between mb-6 px-1'>
						<div>
							<h3
								className='text-lg font-bold tracking-tight'
								style={{
									color: colors.brand.navy,
									fontFamily:
										'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
								}}>
								Conversations
							</h3>
							<p
								className='text-xs mt-0.5 opacity-70'
								style={{
									color: colors.neutral.slateGray,
									fontFamily:
										'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
								}}>
								{sessionList.length}{' '}
								{sessionList.length === 1 ? 'chat' : 'chats'}
							</p>
						</div>
						<div className='flex items-center gap-1'>
							<IconButton
								onClick={onStartNewChat}
								icon={<FaPlus className='w-3 h-3' />}
								title='New chat'
								variant='default'
							/>
							<IconButton
								onClick={() => setIsSearchOpen(true)}
								icon={<FaSearch className='w-3 h-3' />}
								title='Search chats'
								variant='default'
							/>
						</div>
					</div>

					{/* Chat List */}
					<div className='flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent'>
						{sessionList.length === 0 ? (
							<div className='text-center py-12 px-4'>
								<div
									className='w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center'
									style={{
										backgroundColor: colors.ui.blue50,
										color: colors.brand.blue,
									}}>
									<span className='text-2xl'>💬</span>
								</div>
								<div
									className='text-base font-semibold mb-2'
									style={{
										color: colors.neutral.textBlack,
										fontFamily:
											'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
									}}>
									No conversations yet
								</div>
								<div
									className='text-sm leading-relaxed'
									style={{
										color: colors.neutral.slateGray,
										fontFamily:
											'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
									}}>
									Start a new chat to begin exploring with Wizlop
								</div>
								<button
									onClick={onStartNewChat}
									className='mt-4 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105'
									style={{
										backgroundColor: colors.brand.blue,
										color: 'white',
										fontFamily:
											'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor =
											colors.supporting.lightBlue;
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = colors.brand.blue;
									}}>
									Start First Chat
								</button>
							</div>
						) : (
							<div className='space-y-1'>
								{sessionList
									.filter((session) => session.id && session.title)
									.map((session, index, filteredArray) => (
										<div key={session.id}>
											<ChatSessionItem
												session={session}
												isActive={session.id === activeSessionId}
												isDropdownOpen={dropdownIndex === index}
												onSessionClick={() => {
													if (session.id !== activeSessionId) {
														onLoadOldMessages(session.id);
													}
												}}
												onToggleDropdown={() => onToggleDropdown(index)}
												onDeleteSession={() => onDeleteSession(session.id)}
												onCloseDropdown={handleCloseDropdown}
											/>
											{/* Add divider line between sessions, but not after the last one */}
											{index < filteredArray.length - 1 && (
												<div className='flex justify-center py-2'>
													<div
														className='w-16 h-px'
														style={{
															backgroundColor: colors.ui.gray200,
															opacity: 0.6,
														}}
													/>
												</div>
											)}
										</div>
									))}
							</div>
						)}
					</div>
				</div>
			</div>

			<SearchOverlay
				isOpen={isSearchOpen}
				setIsOpen={setIsSearchOpen}
				sessionList={sessionList}
				loadOldMessages={onLoadOldMessages}
				currentSessionId={activeSessionId || ''}
			/>
		</>
	);
};

export default ChatHistorySection;
