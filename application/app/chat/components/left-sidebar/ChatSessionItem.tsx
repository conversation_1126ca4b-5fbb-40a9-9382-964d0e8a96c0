/** @format */

import { Session } from 'app/chat/types';
import { colors } from 'app/colors';
import React, { useEffect, useRef } from 'react';
import { FaEllipsisH, FaTrash } from 'react-icons/fa';

interface ChatSessionItemProps {
	session: Session;
	isActive: boolean;
	isDropdownOpen: boolean;
	onSessionClick: () => void;
	onToggleDropdown: () => void;
	onDeleteSession: () => void;
	onCloseDropdown: () => void;
}

const ChatSessionItem: React.FC<ChatSessionItemProps> = ({
	session,
	isActive,
	isDropdownOpen,
	onSessionClick,
	onToggleDropdown,
	onDeleteSession,
	onCloseDropdown,
}) => {
	const dropdownRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				onCloseDropdown();
			}
		};

		if (isDropdownOpen) {
			document.addEventListener('mousedown', handleClickOutside);
			return () =>
				document.removeEventListener('mousedown', handleClickOutside);
		}
	}, [isDropdownOpen, onCloseDropdown]);

	return (
		<div className={`relative ${isDropdownOpen ? 'z-[10000]' : 'z-10'}`}>
			<div
				className='flex items-center justify-between py-2 px-3 cursor-pointer transition-all duration-200 group hover:translate-x-1'
				style={{
					backgroundColor: 'transparent',
					borderLeft: `3px solid ${
						isActive ? colors.brand.blue : 'transparent'
					}`,
					color: isActive ? colors.brand.navy : colors.neutral.textBlack,
				}}
				onClick={onSessionClick}>
				<div className='flex-1 min-w-0 pr-2'>
					<div
						className={`text-sm font-medium truncate transition-all duration-200 ${
							isActive ? 'text-base' : ''
						}`}
						style={{
							fontFamily:
								'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
							fontWeight: isActive ? '600' : '500',
							color: isActive ? colors.brand.navy : colors.neutral.slateGray,
						}}>
						{session.title}
					</div>
					{isActive && (
						<div
							className='text-xs mt-0.5 opacity-70'
							style={{
								color: colors.brand.blue,
								fontFamily:
									'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
							}}>
							Active conversation
						</div>
					)}
				</div>

				<button
					onClick={(e) => {
						e.stopPropagation();
						onToggleDropdown();
					}}
					className='p-1.5 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-110 border'
					style={{
						color: colors.neutral.slateGray,
						backgroundColor: 'transparent',
						borderColor: colors.ui.gray200,
					}}
					onMouseEnter={(e) => {
						e.currentTarget.style.backgroundColor = colors.ui.gray100;
						e.currentTarget.style.color = colors.brand.blue;
						e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
					}}
					onMouseLeave={(e) => {
						e.currentTarget.style.backgroundColor = 'transparent';
						e.currentTarget.style.color = colors.neutral.slateGray;
						e.currentTarget.style.boxShadow = 'none';
					}}>
					<FaEllipsisH size={10} />
				</button>
			</div>

			{isDropdownOpen && (
				<div
					ref={dropdownRef}
					className='absolute right-0 top-full mt-1 w-32 shadow-lg border'
					style={{
						backgroundColor: 'rgba(255, 255, 255, 0.98)',
						borderColor: colors.ui.gray200,
						backdropFilter: 'blur(12px)',
						boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
						zIndex: 99999,
						position: 'absolute',
					}}>
					<button
						onClick={(e) => {
							e.stopPropagation();
							onDeleteSession();
							onCloseDropdown();
						}}
						className='w-full px-3 py-2 text-left text-xs transition-all duration-200 flex items-center gap-2 border'
						style={{
							color: colors.utility.error,
							fontFamily:
								'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
							borderColor: colors.utility.errorLight,
							backgroundColor: 'transparent',
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.utility.errorLight;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = 'transparent';
						}}>
						<FaTrash size={10} />
						<span>Delete</span>
					</button>
				</div>
			)}
		</div>
	);
};

export default ChatSessionItem;
