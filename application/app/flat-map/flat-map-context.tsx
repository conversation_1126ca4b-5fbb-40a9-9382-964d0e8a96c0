'use client';

import { createContext, useContext } from 'react';

interface FlatMapNavContextType {
	focusOnUserLocation: () => void;
}

export const FlatMapNavContext = createContext<FlatMapNavContextType | null>(null);

export const useFlatMapNav = () => {
	const context = useContext(FlatMapNavContext);
	if (!context) {
		throw new Error('useFlatMapNav must be used within a FlatMapNavContext');
	}
	return context;
};
