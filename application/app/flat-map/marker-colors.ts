/** @format */

import { colors } from '@/app/colors';
import { POI_CATEGORIES_DATA } from '@/app/shared/poi/constants';

// Category border colors - each category gets a distinct border color
export const CATEGORY_BORDER_COLORS: Record<string, string> = {
	'Food & Drink': colors.supporting['sup-color1'], // Vibrant Coral Red
	'Cultural & Creative Experiences': colors.supporting['sup-color6'], // Soft Purple
	'Sports & Fitness': colors.brand.green, // Vibrant Green
	Entertainment: colors.supporting['sup-color3'], // Electric Blue
	'Shopping & Markets': colors.supporting['sup-color5'], // Warm Yellow
	'Outdoor & Nature': colors.brand.accent, // Nature Green
	'Wellness & Beauty': colors.supporting['sup-color4'], // Mint Green
	Transportation: colors.brand.primary, // Modern Sky Blue
};

// Color palette for subcategories - 12 distinct colors for each category
const SUBCATEGORY_COLOR_PALETTE = [
	colors.utility.success, // Vibrant green
	colors.brand.primary, // Sky blue
	colors.supporting['sup-color1'], // Coral red
	colors.supporting['sup-color2'], // Turquoise
	colors.supporting['sup-color3'], // Electric blue
	colors.supporting['sup-color4'], // Mint green
	colors.supporting['sup-color5'], // Warm yellow
	colors.supporting['sup-color6'], // Soft purple
	colors.brand.accent, // Nature green
	colors.brand.green, // Vibrant green (different shade)
	colors.ui.gray500, // Dark gray
	colors.ui.gray400, // Medium gray
];

// Subcategory fill colors - unique color per subcategory within each category
export const getSubcategoryFillColor = (
	subcategory: string,
	category: string
): string => {
	// Find the subcategory in the category data
	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];

	if (!categoryData) {
		return colors.ui['gray-5']; // Default gray
	}

	const subcategoryIndex = categoryData.subcategories.findIndex(
		(sub) => sub.name === subcategory
	);

	if (subcategoryIndex === -1) {
		return colors.ui['gray-5']; // Default gray
	}

	// Assign color based on subcategory index within the category
	// This ensures each subcategory in a category gets a unique color
	return SUBCATEGORY_COLOR_PALETTE[
		subcategoryIndex % SUBCATEGORY_COLOR_PALETTE.length
	];
};

// Get border color for a category
export const getCategoryBorderColor = (category: string): string => {
	return CATEGORY_BORDER_COLORS[category] || colors.ui.gray500; // Default dark gray
};

// Create marker style for POI
export const getPOIMarkerStyle = (
	category: string,
	subcategory?: string
): {
	borderColor: string;
	fillColor: string;
	borderWidth: number;
	radius: number;
} => {
	const borderColor = getCategoryBorderColor(category);
	const fillColor = subcategory
		? getSubcategoryFillColor(subcategory, category)
		: colors.ui.gray400; // Default fill if no subcategory

	return {
		borderColor,
		fillColor,
		borderWidth: 3, // Thicker border to make category color more visible
		radius: 10, // Slightly larger for better visibility
	};
};

// Helper function to get all category colors for legend/filter UI
export const getAllCategoryColors = (): Array<{
	category: string;
	color: string;
}> => {
	return Object.entries(CATEGORY_BORDER_COLORS).map(([category, color]) => ({
		category,
		color,
	}));
};

// Helper function to get subcategory colors for a specific category
export const getSubcategoryColors = (
	category: string
): Array<{
	subcategory: string;
	color: string;
	importance: number;
	index: number;
}> => {
	const categoryData =
		POI_CATEGORIES_DATA[category as keyof typeof POI_CATEGORIES_DATA];

	if (!categoryData) {
		return [];
	}

	return categoryData.subcategories.map((sub, index) => ({
		subcategory: sub.name,
		color: getSubcategoryFillColor(sub.name, category),
		importance: sub.importance,
		index: index, // Add index for reference
	}));
};
