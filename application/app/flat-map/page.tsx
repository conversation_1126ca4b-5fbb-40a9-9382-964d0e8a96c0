/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LocationSetup,
	useLocationManager,
	useLocationSetup,
} from '@/app/shared/locationManager';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import {
	FaComments,
	FaInfo,
	FaLocationArrow,
	FaMapMarkerAlt,
} from 'react-icons/fa';
import { FlatMapComponent } from './flat-map';
import { FlatMapNavContext } from './flat-map-context';

export default function FlatMapPage() {
	const router = useRouter();
	const [showInfo, setShowInfo] = useState(false);

	// Location management
	const {
		location: userLocation,
		isLoading: isLocationLoading,
		error: locationError,
	} = useLocationManager();

	const [currentLocation, setCurrentLocation] = useState<{
		lat: number;
		lng: number;
	} | null>(null);

	const { showLocationSetup, handleLocationSetupComplete } = useLocationSetup();

	// Focus on user location function for context
	const focusOnUserLocation = useCallback(() => {
		if (userLocation) {
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});
		}
	}, [userLocation]);

	const handleToggleFlatMapInfo = () => {
		setShowInfo(!showInfo);
	};

	if (showLocationSetup) {
		return (
			<FlatMapNavContext.Provider value={{ focusOnUserLocation }}>
				<div className='min-h-screen w-full overflow-hidden'>
					{/* Location Setup Modal/Overlay */}
					<LocationSetup
						isOpen={showLocationSetup}
						onComplete={handleLocationSetupComplete}
						pageContext='globe'
						isModal={false}
						setCurrentLocation={setCurrentLocation}
						title='Location Setup Required'
						subtitle='To explore the interactive flat map, we need to know your location or you can set it manually.'
					/>
				</div>
			</FlatMapNavContext.Provider>
		);
	}

	return (
		<FlatMapNavContext.Provider value={{ focusOnUserLocation }}>
			<div className='min-h-screen w-full overflow-hidden'>
				{/* Floating Map Controls */}
				<div
					className='fixed top-4 md:top-8 left-4 md:left-8 z-40 flex flex-col gap-2 md:gap-3 backdrop-blur-sm shadow-lg px-3 md:px-4 py-3 border items-center'
					style={{
						backgroundColor: 'rgba(255, 255, 255, 0.9)',
						borderColor: colors.ui.gray200,
					}}>
					{/* Logo to Home */}
					<button
						onClick={() => router.push('/')}
						className='p-2 transition-all duration-200 flex items-center justify-center w-10 h-10 border'
						title='Home'
						style={{
							backgroundColor: colors.ui.gray100,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<Image
							src='/logo/192x192.png'
							alt='Home'
							width={24}
							height={24}
							priority
						/>
					</button>

					{/* Go to User Location button */}
					<button
						onClick={() => {
							if (userLocation) {
								focusOnUserLocation();
							} else {
								alert('No location available. Please set your location.');
							}
						}}
						className={`flex items-center justify-center w-10 h-10 transition-all duration-200 border ${
							!userLocation ? 'opacity-50 cursor-not-allowed' : ''
						}`}
						style={{
							background: `linear-gradient(135deg, ${colors.brand.primary} 0%, ${colors.brand.accent} 100%)`,
							color: 'white',
							borderColor: colors.brand.primary,
							boxShadow: `0 2px 8px ${colors.brand.primary}40`,
						}}
						title={
							userLocation ? 'Go to Your Location' : 'No location available'
						}
						onMouseEnter={(e) => {
							if (userLocation) {
								e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.accent} 0%, ${colors.brand.primary} 100%)`;
								e.currentTarget.style.boxShadow = `0 4px 12px ${colors.brand.accent}60`;
							}
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.background = `linear-gradient(135deg, ${colors.brand.primary} 0%, ${colors.brand.accent} 100%)`;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.brand.primary}40`;
						}}>
						<FaLocationArrow className='w-4 h-4' />
					</button>

					{/* Chat Page button */}
					<button
						onClick={() => router.push('/chat')}
						className='flex items-center justify-center w-10 h-10 transition-all duration-200 border'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
							borderColor: colors.ui.blue200,
							boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
						}}
						title='Chat'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.blue200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.blue200}40`;
						}}>
						<FaComments className='w-4 h-4' />
					</button>

					{/* Globe Page button */}
					<button
						onClick={() => router.push('/globe')}
						className='flex items-center justify-center w-10 h-10 transition-all duration-200 border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.supporting.teal,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						title='3D Globe View'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaMapMarkerAlt className='w-4 h-4' />
					</button>

					{/* POI Page button */}
					<button
						onClick={() => router.push('/pois')}
						className='flex items-center justify-center w-10 h-10 transition-all duration-200 border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.supporting.teal,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						title='Places of Interest'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaMapMarkerAlt className='w-4 h-4' />
					</button>
				</div>

				{/* Main Flat Map Component */}
				<FlatMapComponent
					userLocation={userLocation}
					currentLocation={currentLocation}
					onLocationChange={setCurrentLocation}
					showInfo={showInfo}
				/>

				{/* Info Button at Bottom */}
				<div className='fixed bottom-4 md:bottom-8 left-1/2 transform -translate-x-1/2 z-40'>
					<button
						onClick={handleToggleFlatMapInfo}
						className='flex items-center justify-center w-12 h-12 md:w-14 md:h-14 transition-all duration-200 border shadow-lg'
						style={{
							backgroundColor: showInfo ? colors.ui.blue200 : colors.ui.blue100,
							color: colors.brand.blue,
							borderColor: colors.ui.blue200,
							boxShadow: `0 4px 16px ${colors.ui.blue200}60`,
						}}
						title='Info & Navigation'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue200;
							e.currentTarget.style.boxShadow = `0 6px 20px ${colors.ui.blue200}80`;
							e.currentTarget.style.transform = 'scale(1.05)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = showInfo
								? colors.ui.blue200
								: colors.ui.blue100;
							e.currentTarget.style.boxShadow = `0 4px 16px ${colors.ui.blue200}60`;
							e.currentTarget.style.transform = 'scale(1)';
						}}>
						<FaInfo className='w-5 h-5 md:w-6 md:h-6' />
					</button>
				</div>

				{/* Location Setup Modal/Overlay */}
				<LocationSetup
					isOpen={showLocationSetup}
					onComplete={handleLocationSetupComplete}
					pageContext='globe'
					isModal={false}
					setCurrentLocation={setCurrentLocation}
					title='Location Setup Required'
					subtitle='To explore the interactive flat map, we need to know your location or you can set it manually.'
				/>
			</div>
		</FlatMapNavContext.Provider>
	);
}
