/** @format */

/**
 * Client-safe credit utility functions
 * These functions can be used in both client and server components
 */

/**
 * Convert decimal credits to integer (database stores credits as integers, e.g., 5 = 50 in database)
 */
export function creditsToInteger(amount: number): number {
	return Math.round(amount * 10);
}

/**
 * Convert integer credits to decimal for display
 */
export function creditsToDecimal(amount: number): number {
	return amount / 10;
}

/**
 * Format credits for display with proper decimal places
 */
export function formatCredits(amount: number): string {
	const decimal = creditsToDecimal(amount);
	return decimal.toString();
}

/**
 * Validate credit amount (must be positive)
 */
export function isValidCreditAmount(amount: number): boolean {
	return amount > 0 && Number.isFinite(amount);
}
