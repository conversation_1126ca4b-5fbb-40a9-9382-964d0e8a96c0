# Wizlop Development Rules

## 🎨 COLOR SYSTEM
- **ONLY use colors from `application/app/colors.ts`**
- Never hardcode colors like `#FF0000`, `blue-500`, etc.
- Use `colors.brand.*`, `colors.neutral.*`, `colors.ui.*`, `colors.utility.*`
- For gradients, use `gradients.primary`, `gradients.secondary`, etc.

## 📱 RESPONSIVE BREAKPOINTS
Three-tier responsive system:
- **0-767px**: Mobile design (optimized for 320px)
- **768-1023px**: Tablet design
- **1024px+**: Desktop design

## 🔘 BUTTON STANDARDS
### Primary Buttons
```
className: 'px-6 py-3 font-medium transition-all duration-200'
background: gradients.primary or colors.brand.blue
color: white
```

### Secondary Buttons
```
className: 'px-6 py-3 font-medium transition-all duration-200'
background: colors.ui.gray100
color: colors.neutral.textBlack
border: colors.ui.gray200
```

## 📐 DESIGN SYSTEM RULES
### Square Edge Policy
**CRITICAL RULE**: All cards, containers, buttons, and UI elements MUST have square edges (no rounded corners)
- **NO** `rounded-*` classes allowed on cards, containers, buttons
- **EXCEPTION**: Section titles in small cards (like "Explore Categories") can keep `rounded-full`
- **Examples**:
  - ❌ `rounded-xl`, `rounded-2xl`, `rounded-lg`
  - ✅ No border-radius classes
  - ✅ `rounded-full` ONLY for section title cards

## 📐 LAYOUT SYSTEM
### Container Widths
- **Mobile (0-767px)**: `w-full px-4` (16px padding)
- **Tablet (768-1023px)**: `w-full px-6` (24px padding)
- **Desktop (1024px+)**: `max-w-7xl mx-auto px-12` (48px padding, centered)

### Section Spacing
- **Mobile**: `py-8` (32px vertical)
- **Tablet**: `py-12` (48px vertical)
- **Desktop**: `py-16` (64px vertical)

## 📏 VIEWPORT HEIGHT RULES
### Full Screen Pages
```
className: 'min-h-screen'
Use page scroll, not container scroll
```

### Modal/Overlay Content
```
maxHeight: 'calc(100vh - 100px)'
overflow-y: auto
```

## 🔤 TYPOGRAPHY SCALE
### Headers
- **H1**: `text-2xl md:text-3xl lg:text-4xl font-black`
- **H2**: `text-xl md:text-2xl lg:text-3xl font-bold`
- **H3**: `text-lg md:text-xl lg:text-2xl font-semibold`

### Body Text
- **Large**: `text-base md:text-lg`
- **Normal**: `text-sm md:text-base`
- **Small**: `text-xs md:text-sm`

## 🎯 COMPONENT SPACING
### Cards
- **Padding**: `p-4 md:p-6 lg:p-8`
- **Gap**: `gap-4 md:gap-6 lg:gap-8`

### Forms
- **Input Height**: `py-2 md:py-3`
- **Input Padding**: `px-3 md:px-4`
- **Form Gap**: `gap-4 md:gap-6`

## 🔄 RESPONSIVE PATTERNS
### Flex Layouts
```
Mobile: flex-col
Tablet+: flex-row
Gap: gap-4 md:gap-6 lg:gap-8
```

### Grid Layouts
```
Mobile: grid-cols-1
Tablet: grid-cols-2
Desktop: grid-cols-3 lg:grid-cols-4
```

## 🎨 BACKGROUND PATTERNS
### Global Application Background
**IMPORTANT**: Applied at root level in `LayoutWrapper.tsx` for all pages
```css
background: linear-gradient(180deg,
  rgba(1, 3, 79, 0.2) 0%,
  rgba(163, 247, 181, 0.2) 25%,
  rgba(128, 237, 153, 0.2) 50%,
  rgba(102, 208, 255, 0.2) 75%,
  rgba(51, 194, 255, 0.2) 100%
), #FEFEFE
```
**Note**: Individual pages should NOT add their own background colors

### Page Backgrounds
```
background: transparent (inherit from global)
Use global gradient - DO NOT override
```

### Card Backgrounds
```
background: rgba(255, 255, 255, 0.9)
backdrop-filter: blur(sm)
border: colors.ui.gray200
NO rounded corners - square edges only
```

## 📋 LIST & TABLE RULES
- Integrate with page scroll (no separate container scroll)
- Sticky headers: `sticky top-0 z-10`
- Row hover: `hover:bg-gray-50`
- Mobile: Stack vertically, Desktop: Single row with flex-wrap

## 🚀 PERFORMANCE RULES
- Use `transition-all duration-200` for smooth interactions
- Implement lazy loading for images
- Use `backdrop-blur-sm` for glass effects
- Minimize re-renders with proper React patterns

## 📱 MOBILE-FIRST APPROACH
1. Design for 320px mobile first
2. Enhance for 768px tablet
3. Optimize for 1024px+ desktop
4. Test on actual devices
5. Consider touch targets (min 44px)

## 🎪 ANIMATION GUIDELINES
- Subtle hover effects: `hover:scale-105 hover:shadow-lg`
- Loading states: Use consistent spinners
- Page transitions: Fade in with `opacity` and `transform`
- Keep animations under 300ms for responsiveness

## 🛠️ BUILD PROCESS
- Always run `npm run build:webpack` after major changes
- Test responsive breakpoints before deployment
- Validate color usage from colors.ts
- Check performance impact of animations